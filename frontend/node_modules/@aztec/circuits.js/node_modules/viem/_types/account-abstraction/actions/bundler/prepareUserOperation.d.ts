import type { <PERSON><PERSON>, Narrow } from 'abitype';
import { type ParseAccountErrorType } from '../../../accounts/utils/parseAccount.js';
import { type EstimateFeesPerGasErrorType } from '../../../actions/public/estimateFeesPerGas.js';
import type { Client } from '../../../clients/createClient.js';
import type { Transport } from '../../../clients/transports/createTransport.js';
import type { ErrorType } from '../../../errors/utils.js';
import type { Calls } from '../../../types/calls.js';
import type { Chain } from '../../../types/chain.js';
import type { Hex } from '../../../types/misc.js';
import type { StateOverride } from '../../../types/stateOverride.js';
import type { Assign, OneOf, Prettify, UnionOmit } from '../../../types/utils.js';
import { type EncodeFunctionDataErrorType } from '../../../utils/abi/encodeFunctionData.js';
import { type ConcatErrorType } from '../../../utils/data/concat.js';
import type { SmartAccount } from '../../accounts/types.js';
import type { PaymasterActions } from '../../clients/decorators/paymaster.js';
import type { DeriveSmartAccount, GetSmartAccountParameter } from '../../types/account.js';
import type { DeriveEntryPointVersion, EntryPointVersion } from '../../types/entryPointVersion.js';
import type { UserOperation, UserOperationRequest } from '../../types/userOperation.js';
import { type GetPaymasterDataErrorType } from '../paymaster/getPaymasterData.js';
import { type GetPaymasterStubDataErrorType } from '../paymaster/getPaymasterStubData.js';
declare const defaultParameters: readonly ["factory", "fees", "gas", "paymaster", "nonce", "signature"];
export type PrepareUserOperationParameterType = 'factory' | 'fees' | 'gas' | 'paymaster' | 'nonce' | 'signature';
type FactoryProperties<entryPointVersion extends EntryPointVersion = EntryPointVersion> = (entryPointVersion extends '0.7' ? {
    factory: UserOperation['factory'];
    factoryData: UserOperation['factoryData'];
} : never) | (entryPointVersion extends '0.6' ? {
    initCode: UserOperation['initCode'];
} : never);
type GasProperties<entryPointVersion extends EntryPointVersion = EntryPointVersion> = (entryPointVersion extends '0.7' ? {
    callGasLimit: UserOperation['callGasLimit'];
    preVerificationGas: UserOperation['preVerificationGas'];
    verificationGasLimit: UserOperation['verificationGasLimit'];
    paymasterPostOpGasLimit: UserOperation['paymasterPostOpGasLimit'];
    paymasterVerificationGasLimit: UserOperation['paymasterVerificationGasLimit'];
} : never) | (entryPointVersion extends '0.6' ? {
    callGasLimit: UserOperation['callGasLimit'];
    preVerificationGas: UserOperation['preVerificationGas'];
    verificationGasLimit: UserOperation['verificationGasLimit'];
} : never);
type FeeProperties = {
    maxFeePerGas: UserOperation['maxFeePerGas'];
    maxPriorityFeePerGas: UserOperation['maxPriorityFeePerGas'];
};
type NonceProperties = {
    nonce: UserOperation['nonce'];
};
type PaymasterProperties<entryPointVersion extends EntryPointVersion = EntryPointVersion> = (entryPointVersion extends '0.7' ? {
    paymaster: UserOperation['paymaster'];
    paymasterData: UserOperation['paymasterData'];
    paymasterPostOpGasLimit: UserOperation['paymasterPostOpGasLimit'];
    paymasterVerificationGasLimit: UserOperation['paymasterVerificationGasLimit'];
} : never) | (entryPointVersion extends '0.6' ? {
    paymasterAndData: UserOperation['paymasterAndData'];
} : never);
type SignatureProperties = {
    signature: UserOperation['signature'];
};
export type PrepareUserOperationRequest<account extends SmartAccount | undefined = SmartAccount | undefined, accountOverride extends SmartAccount | undefined = SmartAccount | undefined, calls extends readonly unknown[] = readonly unknown[], _derivedAccount extends SmartAccount | undefined = DeriveSmartAccount<account, accountOverride>, _derivedVersion extends EntryPointVersion = DeriveEntryPointVersion<_derivedAccount>> = Assign<UserOperationRequest<_derivedVersion>, OneOf<{
    calls: Calls<Narrow<calls>>;
} | {
    callData: Hex;
}> & {
    parameters?: readonly PrepareUserOperationParameterType[] | undefined;
    paymaster?: Address | true | {
        /** Retrieves paymaster-related User Operation properties to be used for sending the User Operation. */
        getPaymasterData?: PaymasterActions['getPaymasterData'] | undefined;
        /** Retrieves paymaster-related User Operation properties to be used for gas estimation. */
        getPaymasterStubData?: PaymasterActions['getPaymasterStubData'] | undefined;
    } | undefined;
    /** Paymaster context to pass to `getPaymasterData` and `getPaymasterStubData` calls. */
    paymasterContext?: unknown | undefined;
    /** State overrides for the User Operation call. */
    stateOverride?: StateOverride | undefined;
}>;
export type PrepareUserOperationParameters<account extends SmartAccount | undefined = SmartAccount | undefined, accountOverride extends SmartAccount | undefined = SmartAccount | undefined, calls extends readonly unknown[] = readonly unknown[], request extends PrepareUserOperationRequest<account, accountOverride, calls> = PrepareUserOperationRequest<account, accountOverride, calls>> = request & GetSmartAccountParameter<account, accountOverride>;
export type PrepareUserOperationReturnType<account extends SmartAccount | undefined = SmartAccount | undefined, accountOverride extends SmartAccount | undefined = SmartAccount | undefined, calls extends readonly unknown[] = readonly unknown[], request extends PrepareUserOperationRequest<account, accountOverride, calls> = PrepareUserOperationRequest<account, accountOverride, calls>, _parameters extends PrepareUserOperationParameterType = request['parameters'] extends readonly PrepareUserOperationParameterType[] ? request['parameters'][number] : (typeof defaultParameters)[number], _derivedAccount extends SmartAccount | undefined = DeriveSmartAccount<account, accountOverride>, _derivedVersion extends EntryPointVersion = DeriveEntryPointVersion<_derivedAccount>> = Prettify<UnionOmit<request, 'calls' | 'parameters'> & {
    callData: Hex;
    paymasterAndData: _derivedVersion extends '0.6' ? Hex : undefined;
    sender: UserOperation['sender'];
} & (Extract<_parameters, 'factory'> extends never ? {} : FactoryProperties<_derivedVersion>) & (Extract<_parameters, 'nonce'> extends never ? {} : NonceProperties) & (Extract<_parameters, 'fees'> extends never ? {} : FeeProperties) & (Extract<_parameters, 'gas'> extends never ? {} : GasProperties<_derivedVersion>) & (Extract<_parameters, 'paymaster'> extends never ? {} : PaymasterProperties<_derivedVersion>) & (Extract<_parameters, 'signature'> extends never ? {} : SignatureProperties)>;
export type PrepareUserOperationErrorType = ParseAccountErrorType | GetPaymasterStubDataErrorType | GetPaymasterDataErrorType | EncodeFunctionDataErrorType | ConcatErrorType | EstimateFeesPerGasErrorType | ErrorType;
/**
 * Prepares a User Operation and fills in missing properties.
 *
 * - Docs: https://viem.sh/actions/bundler/prepareUserOperation
 *
 * @param args - {@link PrepareUserOperationParameters}
 * @returns The User Operation. {@link PrepareUserOperationReturnType}
 *
 * @example
 * import { createBundlerClient, http } from 'viem'
 * import { toSmartAccount } from 'viem/accounts'
 * import { mainnet } from 'viem/chains'
 * import { prepareUserOperation } from 'viem/actions'
 *
 * const account = await toSmartAccount({ ... })
 *
 * const client = createBundlerClient({
 *   chain: mainnet,
 *   transport: http(),
 * })
 *
 * const request = await prepareUserOperation(client, {
 *   account,
 *   calls: [{ to: '0x...', value: parseEther('1') }],
 * })
 */
export declare function prepareUserOperation<account extends SmartAccount | undefined, const calls extends readonly unknown[], const request extends PrepareUserOperationRequest<account, accountOverride, calls>, accountOverride extends SmartAccount | undefined = undefined>(client: Client<Transport, Chain | undefined, account>, parameters_: PrepareUserOperationParameters<account, accountOverride, calls, request>): Promise<PrepareUserOperationReturnType<account, accountOverride, calls, request>>;
export {};
//# sourceMappingURL=prepareUserOperation.d.ts.map