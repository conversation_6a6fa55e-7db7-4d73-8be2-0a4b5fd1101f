export declare const gnosis: {
    blockExplorers: {
        readonly default: {
            readonly name: "Gnosisscan";
            readonly url: "https://gnosisscan.io";
            readonly apiUrl: "https://api.gnosisscan.io/api";
        };
    };
    contracts: {
        readonly multicall3: {
            readonly address: "0xca11bde05977b3631167028862be2a173976ca11";
            readonly blockCreated: 21022491;
        };
    };
    id: 100;
    name: "Gnosis";
    nativeCurrency: {
        readonly decimals: 18;
        readonly name: "xDAI";
        readonly symbol: "XDAI";
    };
    rpcUrls: {
        readonly default: {
            readonly http: readonly ["https://rpc.gnosischain.com"];
            readonly webSocket: readonly ["wss://rpc.gnosischain.com/wss"];
        };
    };
    sourceId?: number | undefined;
    testnet?: boolean | undefined;
    custom?: Record<string, unknown> | undefined;
    fees?: import("../../index.js").ChainFees<undefined> | undefined;
    formatters?: undefined;
    serializers?: import("../../index.js").ChainSerializers<undefined, import("../../index.js").TransactionSerializable> | undefined;
};
//# sourceMappingURL=gnosis.d.ts.map