export declare const taikoHekla: {
    blockExplorers: {
        readonly default: {
            readonly name: "Taikoscan";
            readonly url: "https://hekla.taikoscan.network";
        };
    };
    contracts: {
        readonly multicall3: {
            readonly address: "******************************************";
            readonly blockCreated: 59757;
        };
    };
    id: 167009;
    name: "Taiko Hekla L2";
    nativeCurrency: {
        readonly name: "<PERSON><PERSON>";
        readonly symbol: "ETH";
        readonly decimals: 18;
    };
    rpcUrls: {
        readonly default: {
            readonly http: readonly ["https://rpc.hekla.taiko.xyz"];
        };
    };
    sourceId?: number | undefined;
    testnet: true;
    custom?: Record<string, unknown> | undefined;
    fees?: import("../../index.js").ChainFees<undefined> | undefined;
    formatters?: undefined;
    serializers?: import("../../index.js").ChainSerializers<undefined, import("../../index.js").TransactionSerializable> | undefined;
};
//# sourceMappingURL=taikoHekla.d.ts.map