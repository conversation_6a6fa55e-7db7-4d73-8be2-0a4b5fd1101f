export declare const maxInt8: bigint;
export declare const maxInt16: bigint;
export declare const maxInt24: bigint;
export declare const maxInt32: bigint;
export declare const maxInt40: bigint;
export declare const maxInt48: bigint;
export declare const maxInt56: bigint;
export declare const maxInt64: bigint;
export declare const maxInt72: bigint;
export declare const maxInt80: bigint;
export declare const maxInt88: bigint;
export declare const maxInt96: bigint;
export declare const maxInt104: bigint;
export declare const maxInt112: bigint;
export declare const maxInt120: bigint;
export declare const maxInt128: bigint;
export declare const maxInt136: bigint;
export declare const maxInt144: bigint;
export declare const maxInt152: bigint;
export declare const maxInt160: bigint;
export declare const maxInt168: bigint;
export declare const maxInt176: bigint;
export declare const maxInt184: bigint;
export declare const maxInt192: bigint;
export declare const maxInt200: bigint;
export declare const maxInt208: bigint;
export declare const maxInt216: bigint;
export declare const maxInt224: bigint;
export declare const maxInt232: bigint;
export declare const maxInt240: bigint;
export declare const maxInt248: bigint;
export declare const maxInt256: bigint;
export declare const minInt8: bigint;
export declare const minInt16: bigint;
export declare const minInt24: bigint;
export declare const minInt32: bigint;
export declare const minInt40: bigint;
export declare const minInt48: bigint;
export declare const minInt56: bigint;
export declare const minInt64: bigint;
export declare const minInt72: bigint;
export declare const minInt80: bigint;
export declare const minInt88: bigint;
export declare const minInt96: bigint;
export declare const minInt104: bigint;
export declare const minInt112: bigint;
export declare const minInt120: bigint;
export declare const minInt128: bigint;
export declare const minInt136: bigint;
export declare const minInt144: bigint;
export declare const minInt152: bigint;
export declare const minInt160: bigint;
export declare const minInt168: bigint;
export declare const minInt176: bigint;
export declare const minInt184: bigint;
export declare const minInt192: bigint;
export declare const minInt200: bigint;
export declare const minInt208: bigint;
export declare const minInt216: bigint;
export declare const minInt224: bigint;
export declare const minInt232: bigint;
export declare const minInt240: bigint;
export declare const minInt248: bigint;
export declare const minInt256: bigint;
export declare const maxUint8: bigint;
export declare const maxUint16: bigint;
export declare const maxUint24: bigint;
export declare const maxUint32: bigint;
export declare const maxUint40: bigint;
export declare const maxUint48: bigint;
export declare const maxUint56: bigint;
export declare const maxUint64: bigint;
export declare const maxUint72: bigint;
export declare const maxUint80: bigint;
export declare const maxUint88: bigint;
export declare const maxUint96: bigint;
export declare const maxUint104: bigint;
export declare const maxUint112: bigint;
export declare const maxUint120: bigint;
export declare const maxUint128: bigint;
export declare const maxUint136: bigint;
export declare const maxUint144: bigint;
export declare const maxUint152: bigint;
export declare const maxUint160: bigint;
export declare const maxUint168: bigint;
export declare const maxUint176: bigint;
export declare const maxUint184: bigint;
export declare const maxUint192: bigint;
export declare const maxUint200: bigint;
export declare const maxUint208: bigint;
export declare const maxUint216: bigint;
export declare const maxUint224: bigint;
export declare const maxUint232: bigint;
export declare const maxUint240: bigint;
export declare const maxUint248: bigint;
export declare const maxUint256: bigint;
//# sourceMappingURL=number.d.ts.map