import type { Chain } from '../../chains/index.js';
import type { Client } from '../../clients/createClient.js';
import type { Transport } from '../../clients/transports/createTransport.js';
import type { Account } from '../../types/account.js';
import { type GetL1AllowanceParameters, type GetL1AllowanceReturnType } from '../actions/getL1Allowance.js';
import { type GetL1BalanceParameters, type GetL1BalanceReturnType } from '../actions/getL1Balance.js';
import { type GetL1TokenBalanceParameters, type GetL1TokenBalanceReturnType } from '../actions/getL1TokenBalance.js';
export type PublicActionsL1<account extends Account | undefined = Account | undefined> = {
    /**
     * Returns the amount of approved tokens for a specific L1 bridge.
     *
     * - Docs: https://viem.sh/zksync/actions/getL1Allowance
     *
     * @param client - Client to use
     * @param parameters - {@link AllowanceL1Parameters}
     * @returns The amount of approved tokens for a specific L1 bridge. {@link GetL1AllowanceReturnType}
     *
     * @example
     * import { createPublicClient, custom, parseEther } from 'viem'
     * import { base, mainnet } from 'viem/chains'
     * import { publicActionsL1 } from 'viem/zksync'
     *
     * const client = createPublicClient({
     *   chain: mainnet,
     *   transport: custom(window.ethereum),
     * }).extend(publicActionsL1())
     *
     * const data = await client.getL1Allowance({
     *   account: '******************************************',
     *   token: '******************************************'
     *   bridgeAddress: '******************************************',
     * })
     *
     * @example
     * // Account Hoisting
     * import { createWalletClient, http } from 'viem'
     * import { privateKeyToAccount } from 'viem/accounts'
     * import { base, mainnet } from 'viem/chains'
     * import { publicActionsL1 } from 'viem/zksync'
     *
     * const client = createWalletClient({
     *   account: privateKeyToAccount('0x…'),
     *   chain: mainnet,
     *   transport: http(),
     * }).extend(publicActionsL1())
     *
     * const data = await client.getL1Allowance({
     *   token: '******************************************'
     *   bridgeAddress: '******************************************',
     * })
     */
    getL1Allowance: (parameters: GetL1AllowanceParameters<account>) => Promise<GetL1AllowanceReturnType>;
    /**
     * Returns the amount of the ERC20 token the client has on specific address.
     *
     * - Docs: https://viem.sh/zksync/actions/getL1TokenBalance
     *
     * @param client - Client to use
     * @param parameters - {@link GetL1TokenBalanceParameters}
     * @returns The amount of the ERC20 token the client has on specific address. {@link GetL1TokenBalanceReturnType}
     *
     * @example
     * import { createPublicClient, custom, parseEther } from 'viem'
     * import { base, mainnet } from 'viem/chains'
     * import { publicActionsL1 } from 'viem/zksync'
     *
     * const client = createPublicClient({
     *   chain: mainnet,
     *   transport: custom(window.ethereum),
     * }).extend(publicActionsL1())
     *
     * const data = await client.getL1TokenBalance({
     *   account: '******************************************',
     *   token: '******************************************'
     * })
     *
     * @example
     * // Account Hoisting
     * import { createWalletClient, http } from 'viem'
     * import { privateKeyToAccount } from 'viem/accounts'
     * import { base, mainnet } from 'viem/chains'
     * import { publicActionsL1 } from 'viem/zksync'
     *
     * const client = createWalletClient({
     *   account: privateKeyToAccount('0x…'),
     *   chain: mainnet,
     *   transport: http(),
     * }).extend(publicActionsL1())
     *
     * const data = await client.getL1TokenBalance({
     *   token: '******************************************'
     * })
     */
    getL1TokenBalance: (parameters: GetL1TokenBalanceParameters<account>) => Promise<GetL1TokenBalanceReturnType>;
    /**
     * Returns the amount of the token held by the account on the L1 network.
     *
     * - Docs: https://viem.sh/zksync/actions/getL1TokenBalance
     *
     * @param client - Client to use
     * @param parameters - {@link BalanceL1Parameters}
     * @returns Returns the amount of the token held by the account on the L1 network. {@link GetL1BalanceReturnType}
     *
     * @example
     * import { createPublicClient, custom, parseEther } from 'viem'
     * import { base, mainnet } from 'viem/chains'
     * import { publicActionsL1 } from 'viem/zksync'
     *
     * const client = createPublicClient({
     *   chain: mainnet,
     *   transport: custom(window.ethereum),
     * }).extend(publicActionsL1())
     *
     * const data = await client.getL1Balance({
     *   account: '******************************************'
     * })
     *
     * const data = await client.getL1Balance({
     *   account: '******************************************',
     *   token: '******************************************'
     * })
     *
     * @example
     * // Account Hoisting
     * import { createWalletClient, http } from 'viem'
     * import { privateKeyToAccount } from 'viem/accounts'
     * import { base, mainnet } from 'viem/chains'
     * import { publicActionsL1 } from 'viem/zksync'
     *
     * const client = createWalletClient({
     *   account: privateKeyToAccount('0x…'),
     *   chain: mainnet,
     *   transport: http(),
     * }).extend(publicActionsL1())
     *
     * const data = await client.getL1Balance({})
     *
     * const data = await client.getL1Balance({
     *  token: '******************************************'
     * })
     */
    getL1Balance: (...parameters: account extends undefined ? [GetL1BalanceParameters<account>] : [GetL1BalanceParameters<account>] | []) => Promise<GetL1BalanceReturnType>;
};
export declare function publicActionsL1(): <chain extends Chain | undefined = Chain | undefined, account extends Account | undefined = Account | undefined>(client: Client<Transport, chain, account>) => PublicActionsL1<account>;
//# sourceMappingURL=publicL1.d.ts.map