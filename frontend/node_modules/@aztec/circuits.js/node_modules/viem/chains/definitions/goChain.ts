import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js'

export const goChain = /*#__PURE__*/ defineChain({
  id: 60,
  name: '<PERSON><PERSON><PERSON><PERSON>',
  nativeCurrency: {
    decimals: 18,
    name: '<PERSON>',
    symbol: '<PERSON>',
  },
  rpcUrls: {
    default: { http: ['https://rpc.gochain.io'] },
  },
  blockExplorers: {
    default: {
      name: 'GoChain Explorer',
      url: 'https://explorer.gochain.io',
    },
  },
  testnet: false,
})
