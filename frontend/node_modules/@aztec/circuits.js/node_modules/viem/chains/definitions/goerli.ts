import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js'

export const goerli = /*#__PURE__*/ defineChain({
  id: 5,
  name: '<PERSON><PERSON><PERSON>',
  nativeCurrency: { name: '<PERSON><PERSON><PERSON>', symbol: 'ETH', decimals: 18 },
  rpcUrls: {
    default: {
      http: ['https://rpc.ankr.com/eth_goerli'],
    },
  },
  blockExplorers: {
    default: {
      name: 'Etherscan',
      url: 'https://goerli.etherscan.io',
      apiUrl: 'https://api-goerli.etherscan.io/api',
    },
  },
  contracts: {
    ensRegistry: {
      address: '******************************************',
    },
    ensUniversalResolver: {
      address: '******************************************',
      blockCreated: 10_339_206,
    },
    multicall3: {
      address: '******************************************',
      blockCreated: 6507670,
    },
  },
  testnet: true,
})
