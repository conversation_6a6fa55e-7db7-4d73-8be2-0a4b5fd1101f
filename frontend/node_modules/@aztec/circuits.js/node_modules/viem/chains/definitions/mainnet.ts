import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js'

export const mainnet = /*#__PURE__*/ defineChain({
  id: 1,
  name: 'Ethereum',
  nativeCurrency: { name: '<PERSON><PERSON>', symbol: 'ETH', decimals: 18 },
  rpcUrls: {
    default: {
      http: ['https://eth.merkle.io'],
    },
  },
  blockExplorers: {
    default: {
      name: 'Etherscan',
      url: 'https://etherscan.io',
      apiUrl: 'https://api.etherscan.io/api',
    },
  },
  contracts: {
    ensRegistry: {
      address: '******************************************',
    },
    ensUniversalResolver: {
      address: '******************************************',
      blockCreated: 19_258_213,
    },
    multicall3: {
      address: '******************************************',
      blockCreated: 14_353_601,
    },
  },
})
