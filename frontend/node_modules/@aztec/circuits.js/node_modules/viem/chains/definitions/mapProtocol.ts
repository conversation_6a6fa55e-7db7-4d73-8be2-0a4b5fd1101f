import { define<PERSON><PERSON><PERSON> } from '../../utils/chain/defineChain.js'

export const mapProtocol = /*#__PURE__*/ define<PERSON>hain({
  id: 22776,
  name: 'MAP Protocol',
  nativeCurrency: {
    decimals: 18,
    name: '<PERSON><PERSON>',
    symbol: 'MA<PERSON>',
  },
  rpcUrls: {
    default: { http: ['https://rpc.maplabs.io'] },
  },
  blockExplorers: {
    default: {
      name: 'MAPO Scan',
      url: 'https://maposcan.io',
    },
  },
  testnet: false,
})
