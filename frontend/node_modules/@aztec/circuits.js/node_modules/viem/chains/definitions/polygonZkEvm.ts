import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js'

export const polygonZkEvm = /*#__PURE__*/ defineChain({
  id: 1101,
  name: 'Polygon zkEVM',
  nativeCurrency: { name: '<PERSON><PERSON>', symbol: 'ETH', decimals: 18 },
  rpcUrls: {
    default: {
      http: ['https://zkevm-rpc.com'],
    },
  },
  blockExplorers: {
    default: {
      name: 'PolygonScan',
      url: 'https://zkevm.polygonscan.com',
      apiUrl: 'https://api-zkevm.polygonscan.com/api',
    },
  },
  contracts: {
    multicall3: {
      address: '******************************************',
      blockCreated: 57746,
    },
  },
})
