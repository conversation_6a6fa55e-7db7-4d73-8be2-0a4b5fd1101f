import { define<PERSON>hain } from '../../utils/chain/defineChain.js'

export const polygonZkEvmTestnet = /*#__PURE__*/ defineChain({
  id: 1442,
  name: 'Polygon zkEVM Testnet',
  nativeCurrency: { name: '<PERSON><PERSON>', symbol: 'ETH', decimals: 18 },
  rpcUrls: {
    default: {
      http: ['https://rpc.public.zkevm-test.net'],
    },
  },
  blockExplorers: {
    default: {
      name: 'PolygonScan',
      url: 'https://testnet-zkevm.polygonscan.com',
      apiUrl: 'https://testnet-zkevm.polygonscan.com/api',
    },
  },
  testnet: true,
  contracts: {
    multicall3: {
      address: '******************************************',
      blockCreated: 525686,
    },
  },
})
