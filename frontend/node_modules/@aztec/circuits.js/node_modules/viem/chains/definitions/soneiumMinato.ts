import { chainConfig } from '../../op-stack/chainConfig.js'
import { define<PERSON>hain } from '../../utils/chain/defineChain.js'

const sourceId = 11_155_111 // sepolia

export const soneiumMinato = /*#__PURE__*/ defineChain({
  ...chainConfig,
  id: 1946,
  name: 'Soneium Minato Testnet',
  nativeCurrency: { name: 'Sepolia Ether', symbol: 'ETH', decimals: 18 },
  rpcUrls: {
    default: {
      http: ['https://rpc.minato.soneium.org'],
    },
  },
  blockExplorers: {
    default: {
      name: 'Blockscout',
      url: 'https://soneium-minato.blockscout.com',
      apiUrl: 'https://soneium-minato.blockscout.com/api',
    },
  },
  contracts: {
    ...chainConfig.contracts,
    disputeGameFactory: {
      [sourceId]: {
        address: '******************************************',
      },
    },
    l2OutputOracle: {
      [sourceId]: {
        address: '******************************************',
      },
    },
    portal: {
      [sourceId]: {
        address: '******************************************',
        blockCreated: 6466136,
      },
    },
    l1StandardBridge: {
      [sourceId]: {
        address: '******************************************',
        blockCreated: 6466136,
      },
    },
    multicall3: {
      address: '******************************************',
      blockCreated: 1,
    },
  },
  testnet: true,
  sourceId,
})
