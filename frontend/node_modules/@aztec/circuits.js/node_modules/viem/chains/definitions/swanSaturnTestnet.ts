import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js'

export const swanSaturnTestnet = /*#__PURE__*/ define<PERSON>hain({
  id: 2024,
  name: 'Swan Saturn Testnet',
  nativeCurrency: { name: '<PERSON>', symbol: 'sETH', decimals: 18 },
  rpcUrls: {
    default: { http: ['https://saturn-rpc.swanchain.io'] },
  },
  blockExplorers: {
    default: {
      name: 'Swan Explorer',
      url: 'https://saturn-explorer.swanchain.io',
    },
  },
  testnet: true,
})
