import { define<PERSON><PERSON><PERSON> } from '../../utils/chain/defineChain.js'

export const taikoTestnetSepolia = /*#__PURE__*/ define<PERSON>hain({
  id: 167005,
  name: '<PERSON><PERSON> (Alpha-3 Testnet)',
  nativeCurrency: { name: '<PERSON><PERSON>', symbol: 'ETH', decimals: 18 },
  rpcUrls: {
    default: {
      http: ['https://rpc.test.taiko.xyz'],
    },
  },
  blockExplorers: {
    default: {
      name: 'blockscout',
      url: 'https://explorer.test.taiko.xyz',
    },
  },
})
