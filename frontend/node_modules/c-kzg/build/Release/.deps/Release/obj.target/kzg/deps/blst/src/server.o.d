cmd_Release/obj.target/kzg/deps/blst/src/server.o := cc -o Release/obj.target/kzg/deps/blst/src/server.o ../deps/blst/src/server.c '-DNODE_GYP_MODULE_NAME=kzg' '-DUSING_UV_SHARED=1' '-DUSING_V8_SHARED=1' '-DV8_DEPRECATION_WARNINGS=1' '-DV8_DEPRECATION_WARNINGS' '-DV8_IMMINENT_DEPRECATION_WARNINGS' '-D_GLIBCXX_USE_CXX11_ABI=1' '-D_DARWIN_USE_64_BIT_INODE=1' '-D_LARGEFILE_SOURCE' '-D_FILE_OFFSET_BITS=64' '-DOPENSSL_NO_PINSHARED' '-DOPENSSL_THREADS' '-D__BLST_PORTABLE__' '-DNAPI_CPP_EXCEPTIONS' '-DBUILDING_NODE_EXTENSION' -I/Users/<USER>/Library/Caches/node-gyp/18.20.7/include/node -I/Users/<USER>/Library/Caches/node-gyp/18.20.7/src -I/Users/<USER>/Library/Caches/node-gyp/18.20.7/deps/openssl/config -I/Users/<USER>/Library/Caches/node-gyp/18.20.7/deps/openssl/openssl/include -I/Users/<USER>/Library/Caches/node-gyp/18.20.7/deps/uv/include -I/Users/<USER>/Library/Caches/node-gyp/18.20.7/deps/zlib -I/Users/<USER>/Library/Caches/node-gyp/18.20.7/deps/v8/include -I/Users/<USER>/Developer/mess-age/frontend/node_modules/c-kzg/deps/blst/bindings -I/Users/<USER>/Developer/mess-age/frontend/node_modules/c-kzg/deps/c-kzg -I/Users/<USER>/Developer/mess-age/frontend/node_modules/node-addon-api  -O3 -gdwarf-2 -mmacosx-version-min=13.0 -arch arm64 -Wall -Wendif-labels -W -Wno-unused-parameter -fno-strict-aliasing -MMD -MF ./Release/.deps/Release/obj.target/kzg/deps/blst/src/server.o.d.raw   -c
Release/obj.target/kzg/deps/blst/src/server.o: ../deps/blst/src/server.c \
  ../deps/blst/src/keygen.c ../deps/blst/src/consts.h \
  ../deps/blst/src/vect.h ../deps/blst/src/bytes.h \
  ../deps/blst/src/sha256.h ../deps/blst/src/hash_to_field.c \
  ../deps/blst/src/e1.c ../deps/blst/src/point.h \
  ../deps/blst/src/fields.h ../deps/blst/src/errors.h \
  ../deps/blst/src/ec_ops.h ../deps/blst/src/ec_mult.h \
  ../deps/blst/src/map_to_g1.c ../deps/blst/src/e2.c \
  ../deps/blst/src/map_to_g2.c ../deps/blst/src/fp12_tower.c \
  ../deps/blst/src/pairing.c ../deps/blst/src/aggregate.c \
  ../deps/blst/src/exp.c ../deps/blst/src/sqrt.c \
  ../deps/blst/src/sqrt-addchain.h ../deps/blst/src/recip.c \
  ../deps/blst/src/recip-addchain.h ../deps/blst/src/bulk_addition.c \
  ../deps/blst/src/multi_scalar.c ../deps/blst/src/consts.c \
  ../deps/blst/src/vect.c ../deps/blst/src/exports.c \
  ../deps/blst/src/rb_tree.c ../deps/blst/src/cpuid.c
../deps/blst/src/server.c:
../deps/blst/src/keygen.c:
../deps/blst/src/consts.h:
../deps/blst/src/vect.h:
../deps/blst/src/bytes.h:
../deps/blst/src/sha256.h:
../deps/blst/src/hash_to_field.c:
../deps/blst/src/e1.c:
../deps/blst/src/point.h:
../deps/blst/src/fields.h:
../deps/blst/src/errors.h:
../deps/blst/src/ec_ops.h:
../deps/blst/src/ec_mult.h:
../deps/blst/src/map_to_g1.c:
../deps/blst/src/e2.c:
../deps/blst/src/map_to_g2.c:
../deps/blst/src/fp12_tower.c:
../deps/blst/src/pairing.c:
../deps/blst/src/aggregate.c:
../deps/blst/src/exp.c:
../deps/blst/src/sqrt.c:
../deps/blst/src/sqrt-addchain.h:
../deps/blst/src/recip.c:
../deps/blst/src/recip-addchain.h:
../deps/blst/src/bulk_addition.c:
../deps/blst/src/multi_scalar.c:
../deps/blst/src/consts.c:
../deps/blst/src/vect.c:
../deps/blst/src/exports.c:
../deps/blst/src/rb_tree.c:
../deps/blst/src/cpuid.c:
