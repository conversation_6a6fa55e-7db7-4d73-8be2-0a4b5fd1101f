'use client';

import { useState } from 'react';
import { useAztec } from '@/lib/aztec-context';
import { encryptMessage, hashMessage } from '@/lib/crypto';
import { AztecAddress } from '@aztec/aztec.js';

interface MessageComposerProps {
  recipientAddress: string;
}

export function MessageComposer({ recipientAddress }: MessageComposerProps) {
  const { account, pxe, contractAddress } = useAztec();
  const [message, setMessage] = useState('');
  const [isSending, setIsSending] = useState(false);

  const sendMessage = async () => {
    if (!message.trim() || !account || !pxe || !contractAddress) return;

    setIsSending(true);
    try {
      // 1. Encrypt the message client-side
      const encryptedContent = await encryptMessage(message, recipientAddress);
      
      // 2. Create hash of the encrypted content
      const contentHash = hashMessage(encryptedContent);

      // 3. Send only the hash to the contract (not the actual message)
      const recipient = AztecAddress.fromString(recipientAddress);
      
      // This would call the contract's send_message function
      // For now, we'll simulate it
      console.log('Sending message hash to contract:', {
        recipient: recipient.toString(),
        contentHash: contentHash.toString(),
        originalMessage: message, // This is NOT sent to the contract
      });

      // Simulate contract call
      await new Promise(resolve => setTimeout(resolve, 2000));

      setMessage('');
      alert('Message sent successfully! Only the hash was stored on-chain.');
    } catch (error) {
      console.error('Failed to send message:', error);
      alert('Failed to send message. Please try again.');
    } finally {
      setIsSending(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  return (
    <div className="border-t border-white/10 p-4">
      <div className="flex items-end space-x-3">
        <div className="flex-1">
          <textarea
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Type your private message..."
            className="w-full bg-white/5 border border-white/10 rounded-lg px-3 py-2 text-white placeholder-gray-400 resize-none focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            rows={3}
            disabled={isSending}
          />
          <div className="flex items-center justify-between mt-2 text-xs text-gray-400">
            <span>
              🔒 Message will be encrypted client-side before sending
            </span>
            <span>{message.length}/1000</span>
          </div>
        </div>
        <button
          onClick={sendMessage}
          disabled={!message.trim() || isSending}
          className="bg-purple-600 hover:bg-purple-700 disabled:bg-purple-400 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center"
        >
          {isSending ? (
            <>
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Sending...
            </>
          ) : (
            <>
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
              </svg>
              Send
            </>
          )}
        </button>
      </div>
    </div>
  );
}
