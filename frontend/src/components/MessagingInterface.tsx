'use client';

import { useState, useEffect } from 'react';
import { useAztec } from '@/lib/aztec-context';
import { MessageComposer } from './MessageComposer';
import { MessageInbox } from './MessageInbox';
import { ContactList } from './ContactList';
import { KeyManager } from './KeyManager';

export function MessagingInterface() {
  const { account, disconnect } = useAztec();
  const [activeTab, setActiveTab] = useState<'messages' | 'contacts' | 'keys'>('messages');
  const [selectedContact, setSelectedContact] = useState<string | null>(null);

  const tabs = [
    { id: 'messages', label: 'Messages', icon: '💬' },
    { id: 'contacts', label: 'Contacts', icon: '👥' },
    { id: 'keys', label: 'Keys', icon: '🔐' },
  ];

  return (
    <div className="max-w-6xl mx-auto bg-white/10 backdrop-blur-md rounded-lg overflow-hidden">
      {/* Header */}
      <div className="bg-white/5 border-b border-white/10 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="w-10 h-10 bg-purple-500 rounded-full flex items-center justify-center">
              <span className="text-white font-semibold">
                {account?.getAddress().toString().slice(0, 2).toUpperCase()}
              </span>
            </div>
            <div>
              <h2 className="text-white font-semibold">MessAge</h2>
              <p className="text-gray-400 text-sm">
                {account?.getAddress().toString().slice(0, 10)}...
              </p>
            </div>
          </div>
          <button
            onClick={disconnect}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
            </svg>
          </button>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-white/5 border-b border-white/10">
        <nav className="flex">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex-1 px-4 py-3 text-sm font-medium transition-colors ${
                activeTab === tab.id
                  ? 'text-white bg-white/10 border-b-2 border-purple-500'
                  : 'text-gray-400 hover:text-white hover:bg-white/5'
              }`}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Content Area */}
      <div className="h-96 flex">
        {activeTab === 'messages' && (
          <>
            <div className="w-1/3 border-r border-white/10">
              <ContactList
                onSelectContact={setSelectedContact}
                selectedContact={selectedContact}
              />
            </div>
            <div className="flex-1 flex flex-col">
              {selectedContact ? (
                <>
                  <MessageInbox contactAddress={selectedContact} />
                  <MessageComposer recipientAddress={selectedContact} />
                </>
              ) : (
                <div className="flex-1 flex items-center justify-center text-gray-400">
                  <div className="text-center">
                    <svg className="w-12 h-12 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.476L3 21l2.476-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z" />
                    </svg>
                    <p>Select a contact to start messaging</p>
                  </div>
                </div>
              )}
            </div>
          </>
        )}

        {activeTab === 'contacts' && (
          <div className="flex-1">
            <ContactList
              onSelectContact={setSelectedContact}
              selectedContact={selectedContact}
              showManagement={true}
            />
          </div>
        )}

        {activeTab === 'keys' && (
          <div className="flex-1">
            <KeyManager />
          </div>
        )}
      </div>
    </div>
  );
}
