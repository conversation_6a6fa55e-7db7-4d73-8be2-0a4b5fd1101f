'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { createPXEClient, PXE, AztecAddress, Fr } from '@aztec/aztec.js';
import { AccountWalletWithSecretKey } from '@aztec/accounts/single_key';
import { getSchnorrAccount } from '@aztec/accounts/schnorr';

interface AztecContextType {
  pxe: PXE | null;
  account: AccountWalletWithSecretKey | null;
  isConnected: boolean;
  connect: () => Promise<void>;
  disconnect: () => void;
  contractAddress: AztecAddress | null;
}

const AztecContext = createContext<AztecContextType | undefined>(undefined);

export function AztecProvider({ children }: { children: ReactNode }) {
  const [pxe, setPxe] = useState<PXE | null>(null);
  const [account, setAccount] = useState<AccountWalletWithSecretKey | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [contractAddress, setContractAddress] = useState<AztecAddress | null>(null);

  const connect = async () => {
    try {
      // Connect to local Aztec sandbox
      const pxeClient = createPXEClient('http://localhost:8080');
      setPxe(pxeClient);

      // Get or create an account
      const accounts = await pxeClient.getRegisteredAccounts();
      let wallet: AccountWalletWithSecretKey;

      if (accounts.length > 0) {
        // Use existing account
        const secretKey = Fr.random(); // In real app, this would be stored securely
        wallet = await getSchnorrAccount(pxeClient, secretKey, secretKey).waitSetup();
      } else {
        // Create new account
        const secretKey = Fr.random();
        wallet = await getSchnorrAccount(pxeClient, secretKey, secretKey).waitSetup();
      }

      setAccount(wallet);
      setIsConnected(true);

      // Set contract address (this would be deployed contract address)
      // For now, using a placeholder
      setContractAddress(AztecAddress.random());

      console.log('Connected to Aztec network');
    } catch (error) {
      console.error('Failed to connect to Aztec:', error);
      alert('Failed to connect to Aztec network. Make sure the sandbox is running.');
    }
  };

  const disconnect = () => {
    setPxe(null);
    setAccount(null);
    setIsConnected(false);
    setContractAddress(null);
  };

  return (
    <AztecContext.Provider
      value={{
        pxe,
        account,
        isConnected,
        connect,
        disconnect,
        contractAddress,
      }}
    >
      {children}
    </AztecContext.Provider>
  );
}

export function useAztec() {
  const context = useContext(AztecContext);
  if (context === undefined) {
    throw new Error('useAztec must be used within an AztecProvider');
  }
  return context;
}
