import { type AccountWallet, type Auth<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, AztecAddress, CompleteAddress, type NodeInfo } from '@aztec/aztec.js';
import { DefaultAccountInterface } from '../defaults/account_interface.js';
/**
 * Default implementation for an account interface that uses a dapp entrypoint.
 */
export declare class DefaultDappInterface extends DefaultAccountInterface {
    constructor(authWitnessProvider: AuthWitnessProvider, userAddress: CompleteAddress, dappAddress: AztecAddress, nodeInfo: Pick<NodeInfo, 'l1ChainId' | 'rollupVersion'>);
    static createFromUserWallet(wallet: AccountWallet, dappAddress: AztecAddress): DefaultDappInterface;
}
//# sourceMappingURL=dapp_interface.d.ts.map