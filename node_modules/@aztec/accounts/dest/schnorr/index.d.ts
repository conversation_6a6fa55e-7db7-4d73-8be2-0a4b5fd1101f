/**
 * The `@aztec/accounts/schnorr` export provides an account contract implementation that uses Schnorr signatures with a Grumpkin key for authentication, and a separate Grumpkin key for encryption.
 * This is the suggested account contract type for most use cases within Aztec.
 *
 * @packageDocumentation
 */
import { AccountManager, type Salt } from '@aztec/aztec.js/account';
import { type AccountWallet, type AccountWalletWithSecretKey } from '@aztec/aztec.js/wallet';
import { Fr, GrumpkinScalar } from '@aztec/foundation/fields';
import type { ContractArtifact } from '@aztec/stdlib/abi';
import { AztecAddress } from '@aztec/stdlib/aztec-address';
import type { PXE } from '@aztec/stdlib/interfaces/client';
import { SchnorrBaseAccountContract } from './account_contract.js';
export declare const SchnorrAccountContractArtifact: ContractArtifact;
/**
 * Account contract that authenticates transactions using Schnorr signatures
 * verified against a Grumpkin public key stored in an immutable encrypted note.
 * Eagerly loads the contract artifact
 */
export declare class SchnorrAccountContract extends SchnorrBaseAccountContract {
    constructor(signingPrivateKey: GrumpkinScalar);
    getContractArtifact(): Promise<ContractArtifact>;
}
/**
 * Creates an Account Manager that relies on a Grumpkin signing key for authentication.
 * @param pxe - An PXE server instance.
 * @param secretKey - Secret key used to derive all the keystore keys.
 * @param signingPrivateKey - Grumpkin key used for signing transactions.
 * @param salt - Deployment salt.
 * @returns An account manager initialized with the account contract and its deployment params
 */
export declare function getSchnorrAccount(pxe: PXE, secretKey: Fr, signingPrivateKey: GrumpkinScalar, salt?: Salt): Promise<AccountManager>;
/**
 * Gets a wallet for an already registered account using Schnorr signatures.
 * @param pxe - An PXE server instance.
 * @param address - Address for the account.
 * @param signingPrivateKey - Grumpkin key used for signing transactions.
 * @returns A wallet for this account that can be used to interact with a contract instance.
 */
export declare function getSchnorrWallet(pxe: PXE, address: AztecAddress, signingPrivateKey: GrumpkinScalar): Promise<AccountWallet>;
/**
 * Gets a wallet for an already registered account using Schnorr signatures.
 * @param pxe - An PXE server instance.
 * @param secretKey - Secret key used to derive all the keystore keys.
 * @param signingPrivateKey - Grumpkin key used for signing transactions.
 * @param salt - Deployment salt.
 * @returns A wallet for this account that can be used to interact with a contract instance.
 */
export declare function getSchnorrWalletWithSecretKey(pxe: PXE, secretKey: Fr, signingPrivateKey: GrumpkinScalar, salt: Salt): Promise<AccountWalletWithSecretKey>;
/**
 * Compute the address of a schnorr account contract.
 * @param secret - A seed for deriving the signing key and public keys.
 * @param salt - The contract address salt.
 * @param signingPrivateKey - A specific signing private key that's not derived from the secret.
 */
export declare function getSchnorrAccountContractAddress(secret: Fr, salt: Fr, signingPrivateKey?: GrumpkinScalar): Promise<AztecAddress>;
//# sourceMappingURL=index.d.ts.map