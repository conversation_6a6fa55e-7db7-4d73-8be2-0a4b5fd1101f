/**
 * The `@aztec/accounts/single_key` export provides a testing account contract implementation that uses a single Grumpkin key for both authentication and encryption.
 * It is not recommended to use this account type in production.
 *
 * @packageDocumentation
 */
import type { AztecAddress, Fr, GrumpkinScalar } from '@aztec/aztec.js';
import { AccountManager, type Salt } from '@aztec/aztec.js/account';
import { type AccountWallet } from '@aztec/aztec.js/wallet';
import type { ContractArtifact } from '@aztec/stdlib/abi';
import type { PXE } from '@aztec/stdlib/interfaces/client';
import { SingleKeyBaseAccountContract } from './account_contract.js';
export declare const SchnorrSingleKeyAccountContractArtifact: ContractArtifact;
/**
 * Account contract that authenticates transactions using Schnorr signatures verified against
 * the note encryption key, relying on a single private key for both encryption and authentication.
 * Eagerly loads the contract artifact
 */
export declare class SingleKeyAccountContract extends SingleKeyBaseAccountContract {
    constructor(signingPrivateKey: GrumpkinScalar);
    getContractArtifact(): Promise<ContractArtifact>;
}
/**
 * Creates an Account that uses the same Grumpkin key for encryption and authentication.
 * @param pxe - An PXE server instance.
 * @param secretKey - Secret key used to derive all the keystore keys (in this case also used to get signing key).
 * @param salt - Deployment salt.
 * @returns An account manager initialized with the account contract and its deployment params
 */
export declare function getUnsafeSchnorrAccount(pxe: PXE, secretKey: Fr, salt?: Salt): Promise<AccountManager>;
/**
 * Gets a wallet for an already registered account using Schnorr signatures with a single key for encryption and authentication.
 * @param pxe - An PXE server instance.
 * @param address - Address for the account.
 * @param signingPrivateKey - Grumpkin key used for note encryption and signing transactions.
 * @returns A wallet for this account that can be used to interact with a contract instance.
 */
export declare function getUnsafeSchnorrWallet(pxe: PXE, address: AztecAddress, signingPrivateKey: GrumpkinScalar): Promise<AccountWallet>;
//# sourceMappingURL=index.d.ts.map