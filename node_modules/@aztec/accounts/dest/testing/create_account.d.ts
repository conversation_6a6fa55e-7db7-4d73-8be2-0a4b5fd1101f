import { type AccountManager, type PXE, type WaitForProvenOpts, type WaitOpts } from '@aztec/aztec.js';
import { Fr } from '@aztec/foundation/fields';
import type { InitialAccountData } from './configuration.js';
/**
 * Generate a fixed amount of random schnorr account contract instance.
 */
export declare function generateSchnorrAccounts(numberOfAccounts: number): Promise<{
    secret: Fr;
    signingKey: import("@aztec/aztec.js").Fq;
    salt: Fr;
    address: import("@aztec/aztec.js").AztecAddress;
}[]>;
/**
 * Data for deploying funded account.
 */
type DeployAccountData = Pick<InitialAccountData, 'secret' | 'salt'> & {
    /**
     * An optional signingKey if it's not derived from the secret.
     */
    signingKey?: InitialAccountData['signingKey'];
};
/**
 * Deploy schnorr account contract.
 * It will pay for the fee for the deployment itself. So it must be funded with the prefilled public data.
 */
export declare function deployFundedSchnorrAccount(pxe: PXE, account: DeployAccountData, opts?: WaitOpts & {
    /**
     * Whether or not to skip registering contract class.
     */
    skipClassRegistration?: boolean;
}, waitForProvenOptions?: WaitForProvenOpts): Promise<AccountManager>;
/**
 * Deploy schnorr account contracts.
 * They will pay for the fees for the deployment themselves. So they must be funded with the prefilled public data.
 */
export declare function deployFundedSchnorrAccounts(pxe: PXE, accounts: DeployAccountData[], opts?: WaitOpts & {
    /**
     * Whether or not to skip registering contract class.
     */
    skipClassRegistration?: boolean;
}, waitForProvenOptions?: WaitForProvenOpts): Promise<AccountManager[]>;
export {};
//# sourceMappingURL=create_account.d.ts.map