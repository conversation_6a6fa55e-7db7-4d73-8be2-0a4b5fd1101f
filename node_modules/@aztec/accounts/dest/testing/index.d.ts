/**
 * The `@aztec/accounts/testing` export provides utility methods for testing, in particular in a Sandbox environment.
 *
 * Use {@link getInitialTestAccountsWallets} to obtain a list of wallets for the Sandbox pre-seeded accounts.
 *
 * @packageDocumentation
 */
import { AccountManager, type PXE } from '@aztec/aztec.js';
import type { AccountWalletWithSecretKey } from '@aztec/aztec.js/wallet';
import type { InitialAccountData } from './configuration.js';
export { type InitialAccountData, INITIAL_TEST_ACCOUNT_SALTS, INITIAL_TEST_ENCRYPTION_KEYS, INITIAL_TEST_SECRET_KEYS, INITIAL_TEST_SIGNING_KEYS, } from './configuration.js';
/**
 * Gets the basic information for initial test accounts.
 */
export declare function getInitialTestAccounts(): Promise<InitialAccountData[]>;
/**
 * Gets a collection of account managers for the Aztec accounts that are initially stored in the test environment.
 * @param pxe - PXE instance.
 * @returns A set of AccountManager implementations for each of the initial accounts.
 */
export declare function getInitialTestAccountsManagers(pxe: PXE): Promise<AccountManager[]>;
/**
 * Gets a collection of wallets for the Aztec accounts that are initially stored in the test environment.
 * @param pxe - PXE instance.
 * @returns A set of AccountWallet implementations for each of the initial accounts.
 */
export declare function getInitialTestAccountsWallets(pxe: PXE): Promise<AccountWalletWithSecretKey[]>;
/**
 * Queries a PXE for it's registered accounts.
 * @param pxe - PXE instance.
 * @returns A set of key data for each of the initial accounts.
 */
export declare function getDeployedTestAccounts(pxe: PXE): Promise<InitialAccountData[]>;
/**
 * Queries a PXE for it's registered accounts and returns wallets for those accounts using keys in the initial test accounts.
 * @param pxe - PXE instance.
 * @returns A set of AccountWallet implementations for each of the initial accounts.
 */
export declare function getDeployedTestAccountsWallets(pxe: PXE): Promise<AccountWalletWithSecretKey[]>;
export { deployFundedSchnorrAccount, deployFundedSchnorrAccounts, generateSchnorrAccounts } from './create_account.js';
//# sourceMappingURL=index.d.ts.map