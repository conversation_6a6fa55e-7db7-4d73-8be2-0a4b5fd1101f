import { proxy } from 'comlink';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, BarretenbergApiSync } from '../barretenberg_api/index.js';
import { createMainWorker } from '../barretenberg_wasm/barretenberg_wasm_main/factory/browser/index.js';
import { BarretenbergWasmMain } from '../barretenberg_wasm/barretenberg_wasm_main/index.js';
import { getRemoteBarretenbergWasm } from '../barretenberg_wasm/helpers/index.js';
import { Crs, GrumpkinCrs } from '../crs/index.js';
import { RawBuffer } from '../types/raw_buffer.js';
import { fetchModuleAndThreads } from '../barretenberg_wasm/index.js';
import { createDebugLogger } from '../log/index.js';
export { BarretenbergVerifier } from './verifier.js';
export { UltraHonkBackend, AztecClientBackend } from './backend.js';
/**
 * The main class library consumers interact with.
 * It extends the generated api, and provides a static constructor "new" to compose components.
 */
export class Barr<PERSON>nberg extends BarretenbergApi {
    constructor(worker, wasm, options) {
        super(wasm);
        this.worker = worker;
        this.options = options;
    }
    /**
     * Constructs an instance of Barretenberg.
     * Launches it within a worker. This is necessary as it blocks waiting on child threads to complete,
     * and blocking the main thread in the browser is not allowed.
     * It threads > 1 (defaults to hardware availability), child threads will be created on their own workers.
     */
    static async new(options = {}) {
        const worker = await createMainWorker();
        const wasm = getRemoteBarretenbergWasm(worker);
        const { module, threads } = await fetchModuleAndThreads(options.threads, options.wasmPath, options.logger);
        await wasm.init(module, threads, proxy(options.logger ?? createDebugLogger('bb_wasm_async')), options.memory?.initial, options.memory?.maximum);
        return new Barretenberg(worker, wasm, options);
    }
    async getNumThreads() {
        return await this.wasm.getNumThreads();
    }
    async initSRSForCircuitSize(circuitSize) {
        const crs = await Crs.new(circuitSize + 1, this.options.crsPath, this.options.logger);
        // TODO(https://github.com/AztecProtocol/barretenberg/issues/1129): Do slab allocator initialization?
        // await this.commonInitSlabAllocator(circuitSize);
        await this.srsInitSrs(new RawBuffer(crs.getG1Data()), crs.numPoints, new RawBuffer(crs.getG2Data()));
    }
    async initSRSClientIVC() {
        // crsPath can be undefined
        const crs = await Crs.new(2 ** 20 + 1, this.options.crsPath, this.options.logger);
        const grumpkinCrs = await GrumpkinCrs.new(2 ** 16 + 1, this.options.crsPath, this.options.logger);
        // Load CRS into wasm global CRS state.
        // TODO: Make RawBuffer be default behavior, and have a specific Vector type for when wanting length prefixed.
        await this.srsInitSrs(new RawBuffer(crs.getG1Data()), crs.numPoints, new RawBuffer(crs.getG2Data()));
        await this.srsInitGrumpkinSrs(new RawBuffer(grumpkinCrs.getG1Data()), grumpkinCrs.numPoints);
    }
    async acirInitSRS(bytecode, recursive, honkRecursion) {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const [_total, subgroupSize] = await this.acirGetCircuitSizes(bytecode, recursive, honkRecursion);
        return this.initSRSForCircuitSize(subgroupSize);
    }
    async destroy() {
        await this.wasm.destroy();
        await this.worker.terminate();
    }
    getWasm() {
        return this.wasm;
    }
}
let barretenbergSyncSingletonPromise;
let barretenbergSyncSingleton;
export class BarretenbergSync extends BarretenbergApiSync {
    constructor(wasm) {
        super(wasm);
    }
    static async new(wasmPath, logger = createDebugLogger('bb_wasm_sync')) {
        const wasm = new BarretenbergWasmMain();
        const { module, threads } = await fetchModuleAndThreads(1, wasmPath, logger);
        await wasm.init(module, threads, logger);
        return new BarretenbergSync(wasm);
    }
    static async initSingleton(wasmPath, logger = createDebugLogger('bb_wasm_sync')) {
        if (!barretenbergSyncSingletonPromise) {
            barretenbergSyncSingletonPromise = BarretenbergSync.new(wasmPath, logger);
        }
        barretenbergSyncSingleton = await barretenbergSyncSingletonPromise;
        return barretenbergSyncSingleton;
    }
    static getSingleton() {
        if (!barretenbergSyncSingleton) {
            throw new Error('First call BarretenbergSync.initSingleton() on @aztec/bb.js module.');
        }
        return barretenbergSyncSingleton;
    }
    getWasm() {
        return this.wasm;
    }
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaW5kZXguanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi9zcmMvYmFycmV0ZW5iZXJnL2luZGV4LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLE9BQU8sRUFBRSxLQUFLLEVBQUUsTUFBTSxTQUFTLENBQUM7QUFDaEMsT0FBTyxFQUFFLGVBQWUsRUFBRSxtQkFBbUIsRUFBRSxNQUFNLDhCQUE4QixDQUFDO0FBQ3BGLE9BQU8sRUFBRSxnQkFBZ0IsRUFBRSxNQUFNLG1FQUFtRSxDQUFDO0FBQ3JHLE9BQU8sRUFBRSxvQkFBb0IsRUFBOEIsTUFBTSxzREFBc0QsQ0FBQztBQUN4SCxPQUFPLEVBQUUseUJBQXlCLEVBQUUsTUFBTSx1Q0FBdUMsQ0FBQztBQUNsRixPQUFPLEVBQUUsR0FBRyxFQUFFLFdBQVcsRUFBRSxNQUFNLGlCQUFpQixDQUFDO0FBQ25ELE9BQU8sRUFBRSxTQUFTLEVBQUUsTUFBTSx3QkFBd0IsQ0FBQztBQUNuRCxPQUFPLEVBQUUscUJBQXFCLEVBQUUsTUFBTSwrQkFBK0IsQ0FBQztBQUN0RSxPQUFPLEVBQUUsaUJBQWlCLEVBQUUsTUFBTSxpQkFBaUIsQ0FBQztBQUVwRCxPQUFPLEVBQUUsb0JBQW9CLEVBQUUsTUFBTSxlQUFlLENBQUM7QUFDckQsT0FBTyxFQUFFLGdCQUFnQixFQUFFLGtCQUFrQixFQUFFLE1BQU0sY0FBYyxDQUFDO0FBd0JwRTs7O0dBR0c7QUFDSCxNQUFNLE9BQU8sWUFBYSxTQUFRLGVBQWU7SUFHL0MsWUFDVSxNQUFXLEVBQ25CLElBQWdDLEVBQ2hDLE9BQXVCO1FBRXZCLEtBQUssQ0FBQyxJQUFJLENBQUMsQ0FBQztRQUpKLFdBQU0sR0FBTixNQUFNLENBQUs7UUFLbkIsSUFBSSxDQUFDLE9BQU8sR0FBRyxPQUFPLENBQUM7SUFDekIsQ0FBQztJQUVEOzs7OztPQUtHO0lBQ0gsTUFBTSxDQUFDLEtBQUssQ0FBQyxHQUFHLENBQUMsVUFBMEIsRUFBRTtRQUMzQyxNQUFNLE1BQU0sR0FBRyxNQUFNLGdCQUFnQixFQUFFLENBQUM7UUFDeEMsTUFBTSxJQUFJLEdBQUcseUJBQXlCLENBQTZCLE1BQU0sQ0FBQyxDQUFDO1FBQzNFLE1BQU0sRUFBRSxNQUFNLEVBQUUsT0FBTyxFQUFFLEdBQUcsTUFBTSxxQkFBcUIsQ0FBQyxPQUFPLENBQUMsT0FBTyxFQUFFLE9BQU8sQ0FBQyxRQUFRLEVBQUUsT0FBTyxDQUFDLE1BQU0sQ0FBQyxDQUFDO1FBQzNHLE1BQU0sSUFBSSxDQUFDLElBQUksQ0FDYixNQUFNLEVBQ04sT0FBTyxFQUNQLEtBQUssQ0FBQyxPQUFPLENBQUMsTUFBTSxJQUFJLGlCQUFpQixDQUFDLGVBQWUsQ0FBQyxDQUFDLEVBQzNELE9BQU8sQ0FBQyxNQUFNLEVBQUUsT0FBTyxFQUN2QixPQUFPLENBQUMsTUFBTSxFQUFFLE9BQU8sQ0FDeEIsQ0FBQztRQUNGLE9BQU8sSUFBSSxZQUFZLENBQUMsTUFBTSxFQUFFLElBQUksRUFBRSxPQUFPLENBQUMsQ0FBQztJQUNqRCxDQUFDO0lBRUQsS0FBSyxDQUFDLGFBQWE7UUFDakIsT0FBTyxNQUFNLElBQUksQ0FBQyxJQUFJLENBQUMsYUFBYSxFQUFFLENBQUM7SUFDekMsQ0FBQztJQUVELEtBQUssQ0FBQyxxQkFBcUIsQ0FBQyxXQUFtQjtRQUM3QyxNQUFNLEdBQUcsR0FBRyxNQUFNLEdBQUcsQ0FBQyxHQUFHLENBQUMsV0FBVyxHQUFHLENBQUMsRUFBRSxJQUFJLENBQUMsT0FBTyxDQUFDLE9BQU8sRUFBRSxJQUFJLENBQUMsT0FBTyxDQUFDLE1BQU0sQ0FBQyxDQUFDO1FBQ3RGLHFHQUFxRztRQUNyRyxtREFBbUQ7UUFDbkQsTUFBTSxJQUFJLENBQUMsVUFBVSxDQUFDLElBQUksU0FBUyxDQUFDLEdBQUcsQ0FBQyxTQUFTLEVBQUUsQ0FBQyxFQUFFLEdBQUcsQ0FBQyxTQUFTLEVBQUUsSUFBSSxTQUFTLENBQUMsR0FBRyxDQUFDLFNBQVMsRUFBRSxDQUFDLENBQUMsQ0FBQztJQUN2RyxDQUFDO0lBRUQsS0FBSyxDQUFDLGdCQUFnQjtRQUNwQiwyQkFBMkI7UUFDM0IsTUFBTSxHQUFHLEdBQUcsTUFBTSxHQUFHLENBQUMsR0FBRyxDQUFDLENBQUMsSUFBSSxFQUFFLEdBQUcsQ0FBQyxFQUFFLElBQUksQ0FBQyxPQUFPLENBQUMsT0FBTyxFQUFFLElBQUksQ0FBQyxPQUFPLENBQUMsTUFBTSxDQUFDLENBQUM7UUFDbEYsTUFBTSxXQUFXLEdBQUcsTUFBTSxXQUFXLENBQUMsR0FBRyxDQUFDLENBQUMsSUFBSSxFQUFFLEdBQUcsQ0FBQyxFQUFFLElBQUksQ0FBQyxPQUFPLENBQUMsT0FBTyxFQUFFLElBQUksQ0FBQyxPQUFPLENBQUMsTUFBTSxDQUFDLENBQUM7UUFFbEcsdUNBQXVDO1FBQ3ZDLDhHQUE4RztRQUM5RyxNQUFNLElBQUksQ0FBQyxVQUFVLENBQUMsSUFBSSxTQUFTLENBQUMsR0FBRyxDQUFDLFNBQVMsRUFBRSxDQUFDLEVBQUUsR0FBRyxDQUFDLFNBQVMsRUFBRSxJQUFJLFNBQVMsQ0FBQyxHQUFHLENBQUMsU0FBUyxFQUFFLENBQUMsQ0FBQyxDQUFDO1FBQ3JHLE1BQU0sSUFBSSxDQUFDLGtCQUFrQixDQUFDLElBQUksU0FBUyxDQUFDLFdBQVcsQ0FBQyxTQUFTLEVBQUUsQ0FBQyxFQUFFLFdBQVcsQ0FBQyxTQUFTLENBQUMsQ0FBQztJQUMvRixDQUFDO0lBRUQsS0FBSyxDQUFDLFdBQVcsQ0FBQyxRQUFvQixFQUFFLFNBQWtCLEVBQUUsYUFBc0I7UUFDaEYsNkRBQTZEO1FBQzdELE1BQU0sQ0FBQyxNQUFNLEVBQUUsWUFBWSxDQUFDLEdBQUcsTUFBTSxJQUFJLENBQUMsbUJBQW1CLENBQUMsUUFBUSxFQUFFLFNBQVMsRUFBRSxhQUFhLENBQUMsQ0FBQztRQUNsRyxPQUFPLElBQUksQ0FBQyxxQkFBcUIsQ0FBQyxZQUFZLENBQUMsQ0FBQztJQUNsRCxDQUFDO0lBRUQsS0FBSyxDQUFDLE9BQU87UUFDWCxNQUFNLElBQUksQ0FBQyxJQUFJLENBQUMsT0FBTyxFQUFFLENBQUM7UUFDMUIsTUFBTSxJQUFJLENBQUMsTUFBTSxDQUFDLFNBQVMsRUFBRSxDQUFDO0lBQ2hDLENBQUM7SUFFRCxPQUFPO1FBQ0wsT0FBTyxJQUFJLENBQUMsSUFBSSxDQUFDO0lBQ25CLENBQUM7Q0FDRjtBQUVELElBQUksZ0NBQTJELENBQUM7QUFDaEUsSUFBSSx5QkFBMkMsQ0FBQztBQUVoRCxNQUFNLE9BQU8sZ0JBQWlCLFNBQVEsbUJBQW1CO0lBQ3ZELFlBQW9CLElBQTBCO1FBQzVDLEtBQUssQ0FBQyxJQUFJLENBQUMsQ0FBQztJQUNkLENBQUM7SUFFTyxNQUFNLENBQUMsS0FBSyxDQUFDLEdBQUcsQ0FBQyxRQUFpQixFQUFFLFNBQWdDLGlCQUFpQixDQUFDLGNBQWMsQ0FBQztRQUMzRyxNQUFNLElBQUksR0FBRyxJQUFJLG9CQUFvQixFQUFFLENBQUM7UUFDeEMsTUFBTSxFQUFFLE1BQU0sRUFBRSxPQUFPLEVBQUUsR0FBRyxNQUFNLHFCQUFxQixDQUFDLENBQUMsRUFBRSxRQUFRLEVBQUUsTUFBTSxDQUFDLENBQUM7UUFDN0UsTUFBTSxJQUFJLENBQUMsSUFBSSxDQUFDLE1BQU0sRUFBRSxPQUFPLEVBQUUsTUFBTSxDQUFDLENBQUM7UUFDekMsT0FBTyxJQUFJLGdCQUFnQixDQUFDLElBQUksQ0FBQyxDQUFDO0lBQ3BDLENBQUM7SUFFRCxNQUFNLENBQUMsS0FBSyxDQUFDLGFBQWEsQ0FBQyxRQUFpQixFQUFFLFNBQWdDLGlCQUFpQixDQUFDLGNBQWMsQ0FBQztRQUM3RyxJQUFJLENBQUMsZ0NBQWdDLEVBQUUsQ0FBQztZQUN0QyxnQ0FBZ0MsR0FBRyxnQkFBZ0IsQ0FBQyxHQUFHLENBQUMsUUFBUSxFQUFFLE1BQU0sQ0FBQyxDQUFDO1FBQzVFLENBQUM7UUFFRCx5QkFBeUIsR0FBRyxNQUFNLGdDQUFnQyxDQUFDO1FBQ25FLE9BQU8seUJBQXlCLENBQUM7SUFDbkMsQ0FBQztJQUVELE1BQU0sQ0FBQyxZQUFZO1FBQ2pCLElBQUksQ0FBQyx5QkFBeUIsRUFBRSxDQUFDO1lBQy9CLE1BQU0sSUFBSSxLQUFLLENBQUMscUVBQXFFLENBQUMsQ0FBQztRQUN6RixDQUFDO1FBQ0QsT0FBTyx5QkFBeUIsQ0FBQztJQUNuQyxDQUFDO0lBRUQsT0FBTztRQUNMLE9BQU8sSUFBSSxDQUFDLElBQUksQ0FBQztJQUNuQixDQUFDO0NBQ0YifQ==