{"version": 3, "file": "heap_allocator.d.ts", "sourceRoot": "", "sources": ["../../../../src/barretenberg_wasm/barretenberg_wasm_main/heap_allocator.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,oBAAoB,EAAE,MAAM,YAAY,CAAC;AAEvD;;;;;;;GAOG;AACH,qBAAa,aAAa;IAKZ,OAAO,CAAC,IAAI;IAJxB,OAAO,CAAC,MAAM,CAAgB;IAC9B,OAAO,CAAC,kBAAkB,CAAQ;IAClC,OAAO,CAAC,mBAAmB,CAAQ;gBAEf,IAAI,EAAE,oBAAoB;IAE9C,SAAS,CAAC,OAAO,EAAE,CAAC,UAAU,GAAG,MAAM,CAAC,EAAE;IAmB1C,aAAa,CAAC,OAAO,EAAE,CAAC,MAAM,GAAG,SAAS,CAAC,EAAE;IAgB7C,YAAY,CAAC,GAAG,EAAE,MAAM;IAMxB,OAAO;CAKR"}