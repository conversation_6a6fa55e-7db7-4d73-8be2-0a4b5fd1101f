import pako from 'pako';
// Annoyingly the wasm declares if it's memory is shared or not. So now we need two wasms if we want to be
// able to fallback on "non shared memory" situations.
export async function fetchCode(multithreaded, wasmPath) {
    let url;
    if (wasmPath) {
        const suffix = multithreaded ? '-threads' : '';
        const filePath = wasmPath.split('/').slice(0, -1).join('/');
        const fileNameWithExtensions = wasmPath.split('/').pop();
        const [fileName, ...extensions] = fileNameWithExtensions.split('.');
        url = `${filePath}/${fileName}${suffix}.${extensions.join('.')}`;
    }
    else {
        url = multithreaded
            ? (await import('./barretenberg-threads.js')).default
            : (await import('./barretenberg.js')).default;
    }
    const res = await fetch(url);
    // Default bb wasm is compressed, but user could point it to a non-compressed version
    const maybeCompressedData = await res.arrayBuffer();
    const buffer = new Uint8Array(maybeCompressedData);
    const isGzip = 
    // Check magic number
    buffer[0] === 0x1f &&
        buffer[1] === 0x8b &&
        // Check compression method:
        buffer[2] === 0x08;
    if (isGzip) {
        const decompressedData = pako.ungzip(buffer);
        return decompressedData.buffer;
    }
    else {
        return buffer;
    }
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaW5kZXguanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi9zcmMvYmFycmV0ZW5iZXJnX3dhc20vZmV0Y2hfY29kZS9icm93c2VyL2luZGV4LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLE9BQU8sSUFBSSxNQUFNLE1BQU0sQ0FBQztBQUV4QiwwR0FBMEc7QUFDMUcsc0RBQXNEO0FBQ3RELE1BQU0sQ0FBQyxLQUFLLFVBQVUsU0FBUyxDQUFDLGFBQXNCLEVBQUUsUUFBaUI7SUFDdkUsSUFBSSxHQUFXLENBQUM7SUFDaEIsSUFBSSxRQUFRLEVBQUUsQ0FBQztRQUNiLE1BQU0sTUFBTSxHQUFHLGFBQWEsQ0FBQyxDQUFDLENBQUMsVUFBVSxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUM7UUFDL0MsTUFBTSxRQUFRLEdBQUcsUUFBUSxDQUFDLEtBQUssQ0FBQyxHQUFHLENBQUMsQ0FBQyxLQUFLLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFDO1FBQzVELE1BQU0sc0JBQXNCLEdBQUcsUUFBUSxDQUFDLEtBQUssQ0FBQyxHQUFHLENBQUMsQ0FBQyxHQUFHLEVBQUUsQ0FBQztRQUN6RCxNQUFNLENBQUMsUUFBUSxFQUFFLEdBQUcsVUFBVSxDQUFDLEdBQUcsc0JBQXVCLENBQUMsS0FBSyxDQUFDLEdBQUcsQ0FBQyxDQUFDO1FBQ3JFLEdBQUcsR0FBRyxHQUFHLFFBQVEsSUFBSSxRQUFRLEdBQUcsTUFBTSxJQUFJLFVBQVUsQ0FBQyxJQUFJLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQztJQUNuRSxDQUFDO1NBQU0sQ0FBQztRQUNOLEdBQUcsR0FBRyxhQUFhO1lBQ2pCLENBQUMsQ0FBQyxDQUFDLE1BQU0sTUFBTSxDQUFDLDJCQUEyQixDQUFDLENBQUMsQ0FBQyxPQUFPO1lBQ3JELENBQUMsQ0FBQyxDQUFDLE1BQU0sTUFBTSxDQUFDLG1CQUFtQixDQUFDLENBQUMsQ0FBQyxPQUFPLENBQUM7SUFDbEQsQ0FBQztJQUNELE1BQU0sR0FBRyxHQUFHLE1BQU0sS0FBSyxDQUFDLEdBQUcsQ0FBQyxDQUFDO0lBQzdCLHFGQUFxRjtJQUNyRixNQUFNLG1CQUFtQixHQUFHLE1BQU0sR0FBRyxDQUFDLFdBQVcsRUFBRSxDQUFDO0lBQ3BELE1BQU0sTUFBTSxHQUFHLElBQUksVUFBVSxDQUFDLG1CQUFtQixDQUFDLENBQUM7SUFDbkQsTUFBTSxNQUFNO0lBQ1YscUJBQXFCO0lBQ3JCLE1BQU0sQ0FBQyxDQUFDLENBQUMsS0FBSyxJQUFJO1FBQ2xCLE1BQU0sQ0FBQyxDQUFDLENBQUMsS0FBSyxJQUFJO1FBQ2xCLDRCQUE0QjtRQUM1QixNQUFNLENBQUMsQ0FBQyxDQUFDLEtBQUssSUFBSSxDQUFDO0lBQ3JCLElBQUksTUFBTSxFQUFFLENBQUM7UUFDWCxNQUFNLGdCQUFnQixHQUFHLElBQUksQ0FBQyxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUM7UUFDN0MsT0FBTyxnQkFBZ0IsQ0FBQyxNQUE0QyxDQUFDO0lBQ3ZFLENBQUM7U0FBTSxDQUFDO1FBQ04sT0FBTyxNQUFNLENBQUM7SUFDaEIsQ0FBQztBQUNILENBQUMifQ==