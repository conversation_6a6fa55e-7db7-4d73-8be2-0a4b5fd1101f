/**
 * Convert a 32-byte BE Buffer to a BigInt.
 */
export function buffer32BytesToBigIntBE(buf) {
    return ((buf.readBigUInt64BE(0) << 192n) +
        (buf.readBigUInt64BE(8) << 128n) +
        (buf.readBigUInt64BE(16) << 64n) +
        buf.readBigUInt64BE(24));
}
/**
 * Convert a BE Uint8Array to a BigInt.
 */
export function uint8ArrayToBigIntBE(bytes) {
    const buffer = Buffer.from(bytes);
    return buffer32BytesToBigIntBE(buffer);
}
/**
 * Convert a BigInt to a 32-byte BE Buffer.
 */
export function bigIntToBufferBE(value, byteLength = 32) {
    if (byteLength != 32) {
        throw new Error(`Only 32 bytes supported for conversion from bigint to buffer, attempted byte length: ${byteLength}`);
    }
    const buf = Buffer.alloc(byteLength);
    buf.writeBigUInt64BE(value >> 192n, 0);
    buf.writeBigUInt64BE((value >> 128n) & 0xffffffffffffffffn, 8);
    buf.writeBigUInt64BE((value >> 64n) & 0xffffffffffffffffn, 16);
    buf.writeBigUInt64BE(value & 0xffffffffffffffffn, 24);
    return buf;
}
/**
 * Convert a BigInt to a 32-byte BE Uint8Array.
 */
export function bigIntToUint8ArrayBE(value, byteLength = 32) {
    return new Uint8Array(bigIntToBufferBE(value, byteLength));
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaW5kZXguanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi9zcmMvYmlnaW50LWFycmF5L2luZGV4LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBOztHQUVHO0FBQ0gsTUFBTSxVQUFVLHVCQUF1QixDQUFDLEdBQVc7SUFDakQsT0FBTyxDQUNMLENBQUMsR0FBRyxDQUFDLGVBQWUsQ0FBQyxDQUFDLENBQUMsSUFBSSxJQUFJLENBQUM7UUFDaEMsQ0FBQyxHQUFHLENBQUMsZUFBZSxDQUFDLENBQUMsQ0FBQyxJQUFJLElBQUksQ0FBQztRQUNoQyxDQUFDLEdBQUcsQ0FBQyxlQUFlLENBQUMsRUFBRSxDQUFDLElBQUksR0FBRyxDQUFDO1FBQ2hDLEdBQUcsQ0FBQyxlQUFlLENBQUMsRUFBRSxDQUFDLENBQ3hCLENBQUM7QUFDSixDQUFDO0FBRUQ7O0dBRUc7QUFDSCxNQUFNLFVBQVUsb0JBQW9CLENBQUMsS0FBaUI7SUFDcEQsTUFBTSxNQUFNLEdBQUcsTUFBTSxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQztJQUNsQyxPQUFPLHVCQUF1QixDQUFDLE1BQU0sQ0FBQyxDQUFDO0FBQ3pDLENBQUM7QUFFRDs7R0FFRztBQUNILE1BQU0sVUFBVSxnQkFBZ0IsQ0FBQyxLQUFhLEVBQUUsVUFBVSxHQUFHLEVBQUU7SUFDN0QsSUFBSSxVQUFVLElBQUksRUFBRSxFQUFFLENBQUM7UUFDckIsTUFBTSxJQUFJLEtBQUssQ0FDYix3RkFBd0YsVUFBVSxFQUFFLENBQ3JHLENBQUM7SUFDSixDQUFDO0lBQ0QsTUFBTSxHQUFHLEdBQUcsTUFBTSxDQUFDLEtBQUssQ0FBQyxVQUFVLENBQUMsQ0FBQztJQUNyQyxHQUFHLENBQUMsZ0JBQWdCLENBQUMsS0FBSyxJQUFJLElBQUksRUFBRSxDQUFDLENBQUMsQ0FBQztJQUN2QyxHQUFHLENBQUMsZ0JBQWdCLENBQUMsQ0FBQyxLQUFLLElBQUksSUFBSSxDQUFDLEdBQUcsbUJBQW1CLEVBQUUsQ0FBQyxDQUFDLENBQUM7SUFDL0QsR0FBRyxDQUFDLGdCQUFnQixDQUFDLENBQUMsS0FBSyxJQUFJLEdBQUcsQ0FBQyxHQUFHLG1CQUFtQixFQUFFLEVBQUUsQ0FBQyxDQUFDO0lBQy9ELEdBQUcsQ0FBQyxnQkFBZ0IsQ0FBQyxLQUFLLEdBQUcsbUJBQW1CLEVBQUUsRUFBRSxDQUFDLENBQUM7SUFDdEQsT0FBTyxHQUFHLENBQUM7QUFDYixDQUFDO0FBRUQ7O0dBRUc7QUFDSCxNQUFNLFVBQVUsb0JBQW9CLENBQUMsS0FBYSxFQUFFLFVBQVUsR0FBRyxFQUFFO0lBQ2pFLE9BQU8sSUFBSSxVQUFVLENBQUMsZ0JBQWdCLENBQUMsS0FBSyxFQUFFLFVBQVUsQ0FBQyxDQUFDLENBQUM7QUFDN0QsQ0FBQyJ9