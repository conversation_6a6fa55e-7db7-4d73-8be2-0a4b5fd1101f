"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BarretenbergSync = exports.Barretenberg = exports.AztecClientBackend = exports.UltraHonkBackend = exports.BarretenbergVerifier = void 0;
const comlink_1 = require("comlink");
const index_js_1 = require("../barretenberg_api/index.js");
const index_js_2 = require("../barretenberg_wasm/barretenberg_wasm_main/factory/node/index.js");
const index_js_3 = require("../barretenberg_wasm/barretenberg_wasm_main/index.js");
const index_js_4 = require("../barretenberg_wasm/helpers/index.js");
const index_js_5 = require("../crs/index.js");
const raw_buffer_js_1 = require("../types/raw_buffer.js");
const index_js_6 = require("../barretenberg_wasm/index.js");
const index_js_7 = require("../log/index.js");
var verifier_js_1 = require("./verifier.js");
Object.defineProperty(exports, "BarretenbergVerifier", { enumerable: true, get: function () { return verifier_js_1.BarretenbergVerifier; } });
var backend_js_1 = require("./backend.js");
Object.defineProperty(exports, "UltraHonkBackend", { enumerable: true, get: function () { return backend_js_1.UltraHonkBackend; } });
Object.defineProperty(exports, "AztecClientBackend", { enumerable: true, get: function () { return backend_js_1.AztecClientBackend; } });
/**
 * The main class library consumers interact with.
 * It extends the generated api, and provides a static constructor "new" to compose components.
 */
class Barretenberg extends index_js_1.BarretenbergApi {
    constructor(worker, wasm, options) {
        super(wasm);
        this.worker = worker;
        this.options = options;
    }
    /**
     * Constructs an instance of Barretenberg.
     * Launches it within a worker. This is necessary as it blocks waiting on child threads to complete,
     * and blocking the main thread in the browser is not allowed.
     * It threads > 1 (defaults to hardware availability), child threads will be created on their own workers.
     */
    static async new(options = {}) {
        const worker = await (0, index_js_2.createMainWorker)();
        const wasm = (0, index_js_4.getRemoteBarretenbergWasm)(worker);
        const { module, threads } = await (0, index_js_6.fetchModuleAndThreads)(options.threads, options.wasmPath, options.logger);
        await wasm.init(module, threads, (0, comlink_1.proxy)(options.logger ?? (0, index_js_7.createDebugLogger)('bb_wasm_async')), options.memory?.initial, options.memory?.maximum);
        return new Barretenberg(worker, wasm, options);
    }
    async getNumThreads() {
        return await this.wasm.getNumThreads();
    }
    async initSRSForCircuitSize(circuitSize) {
        const crs = await index_js_5.Crs.new(circuitSize + 1, this.options.crsPath, this.options.logger);
        // TODO(https://github.com/AztecProtocol/barretenberg/issues/1129): Do slab allocator initialization?
        // await this.commonInitSlabAllocator(circuitSize);
        await this.srsInitSrs(new raw_buffer_js_1.RawBuffer(crs.getG1Data()), crs.numPoints, new raw_buffer_js_1.RawBuffer(crs.getG2Data()));
    }
    async initSRSClientIVC() {
        // crsPath can be undefined
        const crs = await index_js_5.Crs.new(2 ** 20 + 1, this.options.crsPath, this.options.logger);
        const grumpkinCrs = await index_js_5.GrumpkinCrs.new(2 ** 16 + 1, this.options.crsPath, this.options.logger);
        // Load CRS into wasm global CRS state.
        // TODO: Make RawBuffer be default behavior, and have a specific Vector type for when wanting length prefixed.
        await this.srsInitSrs(new raw_buffer_js_1.RawBuffer(crs.getG1Data()), crs.numPoints, new raw_buffer_js_1.RawBuffer(crs.getG2Data()));
        await this.srsInitGrumpkinSrs(new raw_buffer_js_1.RawBuffer(grumpkinCrs.getG1Data()), grumpkinCrs.numPoints);
    }
    async acirInitSRS(bytecode, recursive, honkRecursion) {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const [_total, subgroupSize] = await this.acirGetCircuitSizes(bytecode, recursive, honkRecursion);
        return this.initSRSForCircuitSize(subgroupSize);
    }
    async destroy() {
        await this.wasm.destroy();
        await this.worker.terminate();
    }
    getWasm() {
        return this.wasm;
    }
}
exports.Barretenberg = Barretenberg;
let barretenbergSyncSingletonPromise;
let barretenbergSyncSingleton;
class BarretenbergSync extends index_js_1.BarretenbergApiSync {
    constructor(wasm) {
        super(wasm);
    }
    static async new(wasmPath, logger = (0, index_js_7.createDebugLogger)('bb_wasm_sync')) {
        const wasm = new index_js_3.BarretenbergWasmMain();
        const { module, threads } = await (0, index_js_6.fetchModuleAndThreads)(1, wasmPath, logger);
        await wasm.init(module, threads, logger);
        return new BarretenbergSync(wasm);
    }
    static async initSingleton(wasmPath, logger = (0, index_js_7.createDebugLogger)('bb_wasm_sync')) {
        if (!barretenbergSyncSingletonPromise) {
            barretenbergSyncSingletonPromise = BarretenbergSync.new(wasmPath, logger);
        }
        barretenbergSyncSingleton = await barretenbergSyncSingletonPromise;
        return barretenbergSyncSingleton;
    }
    static getSingleton() {
        if (!barretenbergSyncSingleton) {
            throw new Error('First call BarretenbergSync.initSingleton() on @aztec/bb.js module.');
        }
        return barretenbergSyncSingleton;
    }
    getWasm() {
        return this.wasm;
    }
}
exports.BarretenbergSync = BarretenbergSync;
//# sourceMappingURL=data:application/json;base64,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