"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateTypeScriptCode = generateTypeScriptCode;
const tslib_1 = require("tslib");
const fs_1 = tslib_1.__importDefault(require("fs"));
const mappings_js_1 = require("./mappings.js");
const to_camel_case_js_1 = require("./to_camel_case.js");
function generateTypeScriptCode(filename) {
    const fileContent = fs_1.default.readFileSync(filename, 'utf-8');
    const functionDeclarations = JSON.parse(fileContent);
    let output = `// WARNING: FILE CODE GENERATED BY BINDGEN UTILITY. DO NOT EDIT!
/* eslint-disable @typescript-eslint/no-unused-vars */
import { BarretenbergWasmMain, BarretenbergWasmMainWorker } from '../barretenberg_wasm/barretenberg_wasm_main/index.js';
import { BufferDeserializer, NumberDeserializer, VectorDeserializer, BoolDeserializer, StringDeserializer, serializeBufferable, OutputType } from '../serialize/index.js';
import { Fr, Point, Buffer32, Ptr } from '../types/index.js';

`;
    output += generateClass(functionDeclarations);
    output += generateSyncClass(functionDeclarations);
    return output;
}
function generateClass(functionDeclarations) {
    let output = `
export class BarretenbergApi {
  constructor(protected wasm: BarretenbergWasmMainWorker) {}

`;
    for (const { functionName, inArgs, outArgs } of functionDeclarations) {
        try {
            const parameters = inArgs.map(({ name, type }) => `${(0, to_camel_case_js_1.toCamelCase)(name)}: ${(0, mappings_js_1.mapType)(type)}`).join(', ');
            const inArgsVar = `const inArgs = [${inArgs
                .map(arg => (0, to_camel_case_js_1.toCamelCase)(arg.name))
                .join(', ')}].map(serializeBufferable);`;
            const outTypesVar = `const outTypes: OutputType[] = [${outArgs
                .map(arg => (0, mappings_js_1.mapDeserializer)(arg.type))
                .join(', ')}];`;
            const wasmCall = `const result = await this.wasm.callWasmExport('${functionName}', inArgs, outTypes.map(t=>t.SIZE_IN_BYTES));`;
            const outVar = `const out = result.map((r, i) => outTypes[i].fromBuffer(r));`;
            const n = outArgs.length;
            const returnStmt = n === 0 ? 'return;' : n === 1 ? 'return out[0];' : 'return out as any;';
            const returnType = outArgs.length === 0
                ? 'void'
                : outArgs.length === 1
                    ? `${(0, mappings_js_1.mapType)(outArgs[0].type)}`
                    : `[${outArgs.map(a => (0, mappings_js_1.mapType)(a.type)).join(', ')}]`;
            output += `
  async ${(0, to_camel_case_js_1.toCamelCase)(functionName)}(${parameters}): Promise<${returnType}> {
    ${inArgsVar}
    ${outTypesVar}
    ${wasmCall}
    ${outVar}
    ${returnStmt}
  }
`;
        }
        catch (err) {
            throw new Error(`Function ${functionName}: ${err.message}`);
        }
    }
    output += `}`;
    return output;
}
function generateSyncClass(functionDeclarations) {
    let output = `
export class BarretenbergApiSync {
  constructor(protected wasm: BarretenbergWasmMain) {}

`;
    for (const { functionName, inArgs, outArgs } of functionDeclarations) {
        try {
            const parameters = inArgs.map(({ name, type }) => `${(0, to_camel_case_js_1.toCamelCase)(name)}: ${(0, mappings_js_1.mapType)(type)}`).join(', ');
            const inArgsVar = `const inArgs = [${inArgs
                .map(arg => (0, to_camel_case_js_1.toCamelCase)(arg.name))
                .join(', ')}].map(serializeBufferable);`;
            const outTypesVar = `const outTypes: OutputType[] = [${outArgs
                .map(arg => (0, mappings_js_1.mapDeserializer)(arg.type))
                .join(', ')}];`;
            const wasmCall = `const result = this.wasm.callWasmExport('${functionName}', inArgs, outTypes.map(t=>t.SIZE_IN_BYTES));`;
            const outVar = `const out = result.map((r, i) => outTypes[i].fromBuffer(r));`;
            const n = outArgs.length;
            const returnStmt = n === 0 ? 'return;' : n === 1 ? 'return out[0];' : 'return out as any;';
            const returnType = outArgs.length === 0
                ? 'void'
                : outArgs.length === 1
                    ? `${(0, mappings_js_1.mapType)(outArgs[0].type)}`
                    : `[${outArgs.map(a => (0, mappings_js_1.mapType)(a.type)).join(', ')}]`;
            output += `
  ${(0, to_camel_case_js_1.toCamelCase)(functionName)}(${parameters}): ${returnType} {
    ${inArgsVar}
    ${outTypesVar}
    ${wasmCall}
    ${outVar}
    ${returnStmt}
  }
`;
        }
        catch (err) {
            throw new Error(`Function ${functionName}: ${err.message}`);
        }
    }
    output += `}`;
    return output;
}
//# sourceMappingURL=data:application/json;base64,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