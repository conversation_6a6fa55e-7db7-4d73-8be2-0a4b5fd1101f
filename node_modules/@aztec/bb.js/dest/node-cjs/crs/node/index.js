"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GrumpkinCrs = exports.Crs = void 0;
const net_crs_js_1 = require("../net_crs.js");
const fs_1 = require("fs");
const promises_1 = require("fs/promises");
const stream_1 = require("stream");
const os_1 = require("os");
const promises_2 = require("stream/promises");
const index_js_1 = require("../../log/index.js");
/**
 * Generic CRS finder utility class.
 */
class Crs {
    constructor(numPoints, path, logger = (0, index_js_1.createDebugLogger)('crs')) {
        this.numPoints = numPoints;
        this.path = path;
        this.logger = logger;
    }
    static async new(numPoints, crsPath = (0, os_1.homedir)() + '/.bb-crs', logger = (0, index_js_1.createDebugLogger)('crs')) {
        const crs = new Crs(numPoints, crsPath, logger);
        await crs.init();
        return crs;
    }
    async init() {
        (0, fs_1.mkdirSync)(this.path, { recursive: true });
        const g1FileSize = await (0, promises_1.stat)(this.path + '/bn254_g1.dat')
            .then(stats => stats.size)
            .catch(() => 0);
        const g2FileSize = await (0, promises_1.stat)(this.path + '/bn254_g2.dat')
            .then(stats => stats.size)
            .catch(() => 0);
        if (g1FileSize >= this.numPoints * 64 && g1FileSize % 64 == 0 && g2FileSize == 128) {
            this.logger(`Using cached CRS of size ${g1FileSize / 64}`);
            return;
        }
        this.logger(`Downloading CRS of size ${this.numPoints} into ${this.path}`);
        const crs = new net_crs_js_1.NetCrs(this.numPoints);
        const [g1, g2] = await Promise.all([crs.streamG1Data(), crs.streamG2Data()]);
        await Promise.all([
            (0, promises_2.finished)(stream_1.Readable.fromWeb(g1).pipe((0, fs_1.createWriteStream)(this.path + '/bn254_g1.dat'))),
            (0, promises_2.finished)(stream_1.Readable.fromWeb(g2).pipe((0, fs_1.createWriteStream)(this.path + '/bn254_g2.dat'))),
        ]);
    }
    /**
     * G1 points data for prover key.
     * @returns The points data.
     */
    getG1Data() {
        // Ensure length > 0, otherwise we might read a huge file.
        // This is a backup.
        const length = Math.max(this.numPoints, 1) * 64;
        const fd = (0, fs_1.openSync)(this.path + '/bn254_g1.dat', 'r');
        const buffer = new Uint8Array(length);
        (0, fs_1.readSync)(fd, buffer, 0, length, 0);
        (0, fs_1.closeSync)(fd);
        return buffer;
    }
    /**
     * G2 points data for verification key.
     * @returns The points data.
     */
    getG2Data() {
        return (0, fs_1.readFileSync)(this.path + '/bn254_g2.dat');
    }
}
exports.Crs = Crs;
/**
 * Generic Grumpkin CRS finder utility class.
 */
class GrumpkinCrs {
    constructor(numPoints, path, logger = (0, index_js_1.createDebugLogger)('crs')) {
        this.numPoints = numPoints;
        this.path = path;
        this.logger = logger;
    }
    static async new(numPoints, crsPath = (0, os_1.homedir)() + '/.bb-crs', logger = (0, index_js_1.createDebugLogger)('crs')) {
        const crs = new GrumpkinCrs(numPoints, crsPath, logger);
        await crs.init();
        return crs;
    }
    async init() {
        (0, fs_1.mkdirSync)(this.path, { recursive: true });
        const g1FileSize = await (0, promises_1.stat)(this.path + '/grumpkin_g1.flat.dat')
            .then(stats => stats.size)
            .catch(() => 0);
        if (g1FileSize >= this.numPoints * 64 && g1FileSize % 64 == 0) {
            this.logger(`Using cached Grumpkin CRS of size ${g1FileSize / 64}`);
            return;
        }
        this.logger(`Downloading Grumpkin CRS of size ${this.numPoints} into ${this.path}`);
        const crs = new net_crs_js_1.NetGrumpkinCrs(this.numPoints);
        const stream = await crs.streamG1Data();
        await (0, promises_2.finished)(stream_1.Readable.fromWeb(stream).pipe((0, fs_1.createWriteStream)(this.path + '/grumpkin_g1.flat.dat')));
        (0, fs_1.writeFileSync)(this.path + '/grumpkin_size', String(crs.numPoints));
    }
    /**
     * G1 points data for prover key.
     * @returns The points data.
     */
    getG1Data() {
        return (0, fs_1.readFileSync)(this.path + '/grumpkin_g1.flat.dat');
    }
}
exports.GrumpkinCrs = GrumpkinCrs;
//# sourceMappingURL=data:application/json;base64,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