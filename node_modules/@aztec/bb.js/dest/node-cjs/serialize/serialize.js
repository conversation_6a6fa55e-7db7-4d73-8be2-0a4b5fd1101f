"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.boolToBuffer = boolToBuffer;
exports.numToUInt32LE = numToUInt32LE;
exports.numToUInt32BE = numToUInt32BE;
exports.numToInt32BE = numToInt32BE;
exports.numToUInt8 = numToUInt8;
exports.concatenateUint8Arrays = concatenateUint8Arrays;
exports.uint8ArrayToHexString = uint8ArrayToHexString;
exports.serializeBufferToVector = serializeBufferToVector;
exports.serializeBigInt = serializeBigInt;
exports.deserializeBigInt = deserializeBigInt;
exports.serializeDate = serializeDate;
exports.deserializeBufferFromVector = deserializeBufferFromVector;
exports.deserializeBool = deserializeBool;
exports.deserializeUInt32 = deserializeUInt32;
exports.deserializeInt32 = deserializeInt32;
exports.deserializeField = deserializeField;
exports.serializeBufferArrayToVector = serializeBufferArrayToVector;
exports.deserializeArrayFromVector = deserializeArrayFromVector;
exports.serializeBufferable = serializeBufferable;
const raw_buffer_js_1 = require("../types/raw_buffer.js");
// For serializing bool.
function boolToBuffer(b) {
    const buf = new Uint8Array(1);
    buf[0] = b ? 1 : 0;
    return buf;
}
// For serializing numbers to 32 bit little-endian form.
function numToUInt32LE(n, bufferSize = 4) {
    const buf = new Uint8Array(bufferSize);
    new DataView(buf.buffer).setUint32(buf.byteLength - 4, n, true);
    return buf;
}
// For serializing numbers to 32 bit big-endian form.
function numToUInt32BE(n, bufferSize = 4) {
    const buf = new Uint8Array(bufferSize);
    new DataView(buf.buffer).setUint32(buf.byteLength - 4, n, false);
    return buf;
}
// For serializing signed numbers to 32 bit big-endian form.
function numToInt32BE(n, bufferSize = 4) {
    const buf = new Uint8Array(bufferSize);
    new DataView(buf.buffer).setInt32(buf.byteLength - 4, n, false);
    return buf;
}
// For serializing numbers to 8 bit form.
function numToUInt8(n) {
    const buf = new Uint8Array(1);
    buf[0] = n;
    return buf;
}
function concatenateUint8Arrays(arrayOfUint8Arrays) {
    const totalLength = arrayOfUint8Arrays.reduce((prev, curr) => prev + curr.length, 0);
    const result = new Uint8Array(totalLength);
    let length = 0;
    for (const array of arrayOfUint8Arrays) {
        result.set(array, length);
        length += array.length;
    }
    return result;
}
function uint8ArrayToHexString(uint8Array) {
    return uint8Array.reduce((accumulator, byte) => accumulator + byte.toString(16).padStart(2, '0'), '');
}
// For serializing a buffer as a vector.
function serializeBufferToVector(buf) {
    return concatenateUint8Arrays([numToInt32BE(buf.length), buf]);
}
function serializeBigInt(n, width = 32) {
    const buf = new Uint8Array(width);
    for (let i = 0; i < width; i++) {
        buf[width - i - 1] = Number((n >> BigInt(i * 8)) & 0xffn);
    }
    return buf;
}
function deserializeBigInt(buf, offset = 0, width = 32) {
    let result = 0n;
    for (let i = 0; i < width; i++) {
        result = (result << BigInt(8)) | BigInt(buf[offset + i]);
    }
    return { elem: result, adv: width };
}
function serializeDate(date) {
    return serializeBigInt(BigInt(date.getTime()), 8);
}
function deserializeBufferFromVector(vector, offset = 0) {
    const length = new DataView(vector.buffer, vector.byteOffset + offset, 4).getUint32(0, false);
    const adv = 4 + length;
    const elem = vector.slice(offset + 4, offset + adv);
    return { elem, adv };
}
function deserializeBool(buf, offset = 0) {
    const adv = 1;
    const elem = buf[offset] !== 0;
    return { elem, adv };
}
function deserializeUInt32(buf, offset = 0) {
    const adv = 4;
    const elem = new DataView(buf.buffer, buf.byteOffset + offset, adv).getUint32(0, false);
    return { elem, adv };
}
function deserializeInt32(buf, offset = 0) {
    const adv = 4;
    const elem = new DataView(buf.buffer, buf.byteOffset + offset, adv).getInt32(0, false);
    return { elem, adv };
}
function deserializeField(buf, offset = 0) {
    const adv = 32;
    const elem = buf.slice(offset, offset + adv);
    return { elem, adv };
}
// For serializing an array of fixed length elements.
function serializeBufferArrayToVector(arr) {
    return concatenateUint8Arrays([numToUInt32BE(arr.length), ...arr.flat()]);
}
function deserializeArrayFromVector(deserialize, vector, offset = 0) {
    let pos = offset;
    const size = new DataView(vector.buffer, vector.byteOffset + pos, 4).getUint32(0, false);
    pos += 4;
    const arr = new Array(size);
    for (let i = 0; i < size; ++i) {
        const { elem, adv } = deserialize(vector, pos);
        pos += adv;
        arr[i] = elem;
    }
    return { elem: arr, adv: pos - offset };
}
/**
 * Serializes a list of objects contiguously for calling into wasm.
 * @param objs - Objects to serialize.
 * @returns A buffer list with the concatenation of all fields.
 */
function serializeBufferable(obj) {
    if (Array.isArray(obj)) {
        return serializeBufferArrayToVector(obj.map(serializeBufferable));
    }
    else if (obj instanceof raw_buffer_js_1.RawBuffer) {
        return obj;
    }
    else if (obj instanceof Uint8Array) {
        return serializeBufferToVector(obj);
    }
    else if (typeof obj === 'boolean') {
        return boolToBuffer(obj);
    }
    else if (typeof obj === 'number') {
        return numToUInt32BE(obj);
    }
    else if (typeof obj === 'bigint') {
        return serializeBigInt(obj);
    }
    else if (typeof obj === 'string') {
        return serializeBufferToVector(new TextEncoder().encode(obj));
    }
    else {
        return obj.toBuffer();
    }
}
//# sourceMappingURL=data:application/json;base64,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