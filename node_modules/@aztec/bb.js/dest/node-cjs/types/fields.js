"use strict";
var _a, _b;
Object.defineProperty(exports, "__esModule", { value: true });
exports.Fq = exports.Fr = void 0;
const index_js_1 = require("../random/index.js");
const index_js_2 = require("../bigint-array/index.js");
const index_js_3 = require("../serialize/index.js");
// TODO(#4189): Replace with implementation in yarn-project/foundation/src/fields/fields.ts
/**
 * Fr field class.
 * @dev This class is used to represent elements of BN254 scalar field or elements in the base field of Grumpkin.
 * (Grumpkin's scalar field corresponds to BN254's base field and vice versa.)
 */
class Fr {
    constructor(value) {
        // We convert buffer value to bigint to be able to check it fits within modulus
        const valueBigInt = typeof value === 'bigint'
            ? value
            : value instanceof Buffer
                ? (0, index_js_2.buffer32BytesToBigIntBE)(value)
                : (0, index_js_2.uint8ArrayToBigIntBE)(value);
        if (valueBigInt > _a.MAX_VALUE) {
            throw new Error(`Value 0x${valueBigInt.toString(16)} is greater or equal to field modulus.`);
        }
        this.value =
            typeof value === 'bigint' ? (0, index_js_2.bigIntToUint8ArrayBE)(value) : value instanceof Buffer ? new Uint8Array(value) : value;
    }
    static random() {
        const r = (0, index_js_2.uint8ArrayToBigIntBE)((0, index_js_1.randomBytes)(64)) % _a.MODULUS;
        return new this(r);
    }
    static fromBuffer(buffer) {
        const reader = index_js_3.BufferReader.asReader(buffer);
        return new this(reader.readBytes(this.SIZE_IN_BYTES));
    }
    static fromBufferReduce(buffer) {
        const reader = index_js_3.BufferReader.asReader(buffer);
        return new this((0, index_js_2.uint8ArrayToBigIntBE)(reader.readBytes(this.SIZE_IN_BYTES)) % _a.MODULUS);
    }
    static fromString(str) {
        return this.fromBuffer(Buffer.from(str.replace(/^0x/i, ''), 'hex'));
    }
    toBuffer() {
        return this.value;
    }
    toString() {
        return '0x' + (0, index_js_3.uint8ArrayToHexString)(this.toBuffer());
    }
    equals(rhs) {
        return this.value.every((v, i) => v === rhs.value[i]);
    }
    isZero() {
        return this.value.every(v => v === 0);
    }
}
exports.Fr = Fr;
_a = Fr;
Fr.ZERO = new _a(0n);
Fr.MODULUS = 0x30644e72e131a029b85045b68181585d2833e84879b9709143e1f593f0000001n;
Fr.MAX_VALUE = _a.MODULUS - 1n;
Fr.SIZE_IN_BYTES = 32;
/**
 * Fq field class.
 * @dev This class is used to represent elements of BN254 base field or elements in the scalar field of Grumpkin.
 * (Grumpkin's scalar field corresponds to BN254's base field and vice versa.)
 */
class Fq {
    constructor(value) {
        this.value = value;
        if (value > _b.MAX_VALUE) {
            throw new Error(`Fq out of range ${value}.`);
        }
    }
    static random() {
        const r = (0, index_js_2.uint8ArrayToBigIntBE)((0, index_js_1.randomBytes)(64)) % _b.MODULUS;
        return new this(r);
    }
    static fromBuffer(buffer) {
        const reader = index_js_3.BufferReader.asReader(buffer);
        return new this((0, index_js_2.uint8ArrayToBigIntBE)(reader.readBytes(this.SIZE_IN_BYTES)));
    }
    static fromBufferReduce(buffer) {
        const reader = index_js_3.BufferReader.asReader(buffer);
        return new this((0, index_js_2.uint8ArrayToBigIntBE)(reader.readBytes(this.SIZE_IN_BYTES)) % Fr.MODULUS);
    }
    static fromString(str) {
        return this.fromBuffer(Buffer.from(str.replace(/^0x/i, ''), 'hex'));
    }
    toBuffer() {
        return (0, index_js_2.bigIntToBufferBE)(this.value, _b.SIZE_IN_BYTES);
    }
    toString() {
        return '0x' + this.value.toString(16);
    }
    equals(rhs) {
        return this.value === rhs.value;
    }
    isZero() {
        return this.value === 0n;
    }
}
exports.Fq = Fq;
_b = Fq;
Fq.MODULUS = 0x30644e72e131a029b85045b68181585d97816a916871ca8d3c208c16d87cfd47n;
Fq.MAX_VALUE = _b.MODULUS - 1n;
Fq.SIZE_IN_BYTES = 32;
//# sourceMappingURL=data:application/json;base64,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