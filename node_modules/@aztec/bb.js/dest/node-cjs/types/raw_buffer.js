"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RawBuffer = void 0;
// Used when the data is to be sent exactly as is. i.e. no length prefix will be added.
// This is useful for sending structured data that can be parsed-as-you-go, as opposed to just an array of bytes.
class RawBuffer extends Uint8Array {
}
exports.RawBuffer = RawBuffer;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoicmF3X2J1ZmZlci5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uLy4uLy4uL3NyYy90eXBlcy9yYXdfYnVmZmVyLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7OztBQUFBLHVGQUF1RjtBQUN2RixpSEFBaUg7QUFDakgsTUFBYSxTQUFVLFNBQVEsVUFBVTtDQUFHO0FBQTVDLDhCQUE0QyJ9