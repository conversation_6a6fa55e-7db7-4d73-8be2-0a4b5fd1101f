import { BarretenbergWasmMain, BarretenbergWasmMainWorker } from '../barretenberg_wasm/barretenberg_wasm_main/index.js';
import { Fr, Point, Buffer32, Ptr } from '../types/index.js';
export declare class BarretenbergApi {
    protected wasm: BarretenbergWasmMainWorker;
    constructor(wasm: BarretenbergWasmMainWorker);
    pedersenCommit(inputsBuffer: Fr[], ctxIndex: number): Promise<Point>;
    pedersenHash(inputsBuffer: Fr[], hashIndex: number): Promise<Fr>;
    pedersenHashes(inputsBuffer: Fr[], hashIndex: number): Promise<Fr>;
    pedersenHashBuffer(inputBuffer: Uint8Array, hashIndex: number): Promise<Fr>;
    poseidon2Hash(inputsBuffer: Fr[]): Promise<Fr>;
    poseidon2Hashes(inputsBuffer: Fr[]): Promise<Fr>;
    poseidon2Permutation(inputsBuffer: Fr[]): Promise<Fr[]>;
    poseidon2HashAccumulate(inputsBuffer: Fr[]): Promise<Fr>;
    blake2s(data: Uint8Array): Promise<Buffer32>;
    blake2sToField(data: Uint8Array): Promise<Fr>;
    aesEncryptBufferCbc(input: Uint8Array, iv: Uint8Array, key: Uint8Array, length: number): Promise<Uint8Array>;
    aesDecryptBufferCbc(input: Uint8Array, iv: Uint8Array, key: Uint8Array, length: number): Promise<Uint8Array>;
    srsInitSrs(pointsBuf: Uint8Array, numPoints: number, g2PointBuf: Uint8Array): Promise<void>;
    srsInitGrumpkinSrs(pointsBuf: Uint8Array, numPoints: number): Promise<void>;
    testThreads(threads: number, iterations: number): Promise<number>;
    commonInitSlabAllocator(circuitSize: number): Promise<void>;
    acirGetCircuitSizes(constraintSystemBuf: Uint8Array, recursive: boolean, honkRecursion: boolean): Promise<[number, number]>;
    acirProveAndVerifyUltraHonk(constraintSystemBuf: Uint8Array, witnessBuf: Uint8Array): Promise<boolean>;
    acirProveAndVerifyMegaHonk(constraintSystemBuf: Uint8Array, witnessBuf: Uint8Array): Promise<boolean>;
    acirProveAztecClient(ivcInputsBuf: Uint8Array): Promise<[Uint8Array, Uint8Array]>;
    acirVerifyAztecClient(proofBuf: Uint8Array, vkBuf: Uint8Array): Promise<boolean>;
    acirLoadVerificationKey(acirComposerPtr: Ptr, vkBuf: Uint8Array): Promise<void>;
    acirInitVerificationKey(acirComposerPtr: Ptr): Promise<void>;
    acirGetVerificationKey(acirComposerPtr: Ptr): Promise<Uint8Array>;
    acirGetProvingKey(acirComposerPtr: Ptr, acirVec: Uint8Array, recursive: boolean): Promise<Uint8Array>;
    acirVerifyProof(acirComposerPtr: Ptr, proofBuf: Uint8Array): Promise<boolean>;
    acirGetSolidityVerifier(acirComposerPtr: Ptr): Promise<string>;
    acirHonkSolidityVerifier(proofBuf: Uint8Array, vkBuf: Uint8Array): Promise<string>;
    acirSerializeProofIntoFields(acirComposerPtr: Ptr, proofBuf: Uint8Array, numInnerPublicInputs: number): Promise<Fr[]>;
    acirSerializeVerificationKeyIntoFields(acirComposerPtr: Ptr): Promise<[Fr[], Fr]>;
    acirProveUltraHonk(acirVec: Uint8Array, witnessVec: Uint8Array): Promise<Uint8Array>;
    acirProveUltraKeccakHonk(acirVec: Uint8Array, witnessVec: Uint8Array): Promise<Uint8Array>;
    acirProveUltraKeccakZkHonk(acirVec: Uint8Array, witnessVec: Uint8Array): Promise<Uint8Array>;
    acirProveUltraStarknetHonk(acirVec: Uint8Array, witnessVec: Uint8Array): Promise<Uint8Array>;
    acirProveUltraStarknetZkHonk(acirVec: Uint8Array, witnessVec: Uint8Array): Promise<Uint8Array>;
    acirVerifyUltraHonk(proofBuf: Uint8Array, vkBuf: Uint8Array): Promise<boolean>;
    acirVerifyUltraKeccakHonk(proofBuf: Uint8Array, vkBuf: Uint8Array): Promise<boolean>;
    acirVerifyUltraKeccakZkHonk(proofBuf: Uint8Array, vkBuf: Uint8Array): Promise<boolean>;
    acirVerifyUltraStarknetHonk(proofBuf: Uint8Array, vkBuf: Uint8Array): Promise<boolean>;
    acirVerifyUltraStarknetZkHonk(proofBuf: Uint8Array, vkBuf: Uint8Array): Promise<boolean>;
    acirWriteVkUltraHonk(acirVec: Uint8Array): Promise<Uint8Array>;
    acirWriteVkUltraKeccakHonk(acirVec: Uint8Array): Promise<Uint8Array>;
    acirWriteVkUltraKeccakZkHonk(acirVec: Uint8Array): Promise<Uint8Array>;
    acirWriteVkUltraStarknetHonk(acirVec: Uint8Array): Promise<Uint8Array>;
    acirWriteVkUltraStarknetZkHonk(acirVec: Uint8Array): Promise<Uint8Array>;
    acirProofAsFieldsUltraHonk(proofBuf: Uint8Array): Promise<Fr[]>;
    acirVkAsFieldsUltraHonk(vkBuf: Uint8Array): Promise<Fr[]>;
    acirVkAsFieldsMegaHonk(vkBuf: Uint8Array): Promise<Fr[]>;
    acirGatesAztecClient(ivcInputsBuf: Uint8Array): Promise<Uint8Array>;
}
export declare class BarretenbergApiSync {
    protected wasm: BarretenbergWasmMain;
    constructor(wasm: BarretenbergWasmMain);
    pedersenCommit(inputsBuffer: Fr[], ctxIndex: number): Point;
    pedersenHash(inputsBuffer: Fr[], hashIndex: number): Fr;
    pedersenHashes(inputsBuffer: Fr[], hashIndex: number): Fr;
    pedersenHashBuffer(inputBuffer: Uint8Array, hashIndex: number): Fr;
    poseidon2Hash(inputsBuffer: Fr[]): Fr;
    poseidon2Hashes(inputsBuffer: Fr[]): Fr;
    poseidon2Permutation(inputsBuffer: Fr[]): Fr[];
    poseidon2HashAccumulate(inputsBuffer: Fr[]): Fr;
    blake2s(data: Uint8Array): Buffer32;
    blake2sToField(data: Uint8Array): Fr;
    aesEncryptBufferCbc(input: Uint8Array, iv: Uint8Array, key: Uint8Array, length: number): Uint8Array;
    aesDecryptBufferCbc(input: Uint8Array, iv: Uint8Array, key: Uint8Array, length: number): Uint8Array;
    srsInitSrs(pointsBuf: Uint8Array, numPoints: number, g2PointBuf: Uint8Array): void;
    srsInitGrumpkinSrs(pointsBuf: Uint8Array, numPoints: number): void;
    testThreads(threads: number, iterations: number): number;
    commonInitSlabAllocator(circuitSize: number): void;
    acirGetCircuitSizes(constraintSystemBuf: Uint8Array, recursive: boolean, honkRecursion: boolean): [number, number];
    acirProveAndVerifyUltraHonk(constraintSystemBuf: Uint8Array, witnessBuf: Uint8Array): boolean;
    acirProveAndVerifyMegaHonk(constraintSystemBuf: Uint8Array, witnessBuf: Uint8Array): boolean;
    acirProveAztecClient(ivcInputsBuf: Uint8Array): [Uint8Array, Uint8Array];
    acirVerifyAztecClient(proofBuf: Uint8Array, vkBuf: Uint8Array): boolean;
    acirLoadVerificationKey(acirComposerPtr: Ptr, vkBuf: Uint8Array): void;
    acirInitVerificationKey(acirComposerPtr: Ptr): void;
    acirGetVerificationKey(acirComposerPtr: Ptr): Uint8Array;
    acirGetProvingKey(acirComposerPtr: Ptr, acirVec: Uint8Array, recursive: boolean): Uint8Array;
    acirVerifyProof(acirComposerPtr: Ptr, proofBuf: Uint8Array): boolean;
    acirGetSolidityVerifier(acirComposerPtr: Ptr): string;
    acirHonkSolidityVerifier(proofBuf: Uint8Array, vkBuf: Uint8Array): string;
    acirSerializeProofIntoFields(acirComposerPtr: Ptr, proofBuf: Uint8Array, numInnerPublicInputs: number): Fr[];
    acirSerializeVerificationKeyIntoFields(acirComposerPtr: Ptr): [Fr[], Fr];
    acirProveUltraHonk(acirVec: Uint8Array, witnessVec: Uint8Array): Uint8Array;
    acirProveUltraKeccakHonk(acirVec: Uint8Array, witnessVec: Uint8Array): Uint8Array;
    acirProveUltraKeccakZkHonk(acirVec: Uint8Array, witnessVec: Uint8Array): Uint8Array;
    acirProveUltraStarknetHonk(acirVec: Uint8Array, witnessVec: Uint8Array): Uint8Array;
    acirProveUltraStarknetZkHonk(acirVec: Uint8Array, witnessVec: Uint8Array): Uint8Array;
    acirVerifyUltraHonk(proofBuf: Uint8Array, vkBuf: Uint8Array): boolean;
    acirVerifyUltraKeccakHonk(proofBuf: Uint8Array, vkBuf: Uint8Array): boolean;
    acirVerifyUltraKeccakZkHonk(proofBuf: Uint8Array, vkBuf: Uint8Array): boolean;
    acirVerifyUltraStarknetHonk(proofBuf: Uint8Array, vkBuf: Uint8Array): boolean;
    acirVerifyUltraStarknetZkHonk(proofBuf: Uint8Array, vkBuf: Uint8Array): boolean;
    acirWriteVkUltraHonk(acirVec: Uint8Array): Uint8Array;
    acirWriteVkUltraKeccakHonk(acirVec: Uint8Array): Uint8Array;
    acirWriteVkUltraKeccakZkHonk(acirVec: Uint8Array): Uint8Array;
    acirWriteVkUltraStarknetHonk(acirVec: Uint8Array): Uint8Array;
    acirWriteVkUltraStarknetZkHonk(acirVec: Uint8Array): Uint8Array;
    acirProofAsFieldsUltraHonk(proofBuf: Uint8Array): Fr[];
    acirVkAsFieldsUltraHonk(vkBuf: Uint8Array): Fr[];
    acirVkAsFieldsMegaHonk(vkBuf: Uint8Array): Fr[];
    acirGatesAztecClient(ivcInputsBuf: Uint8Array): Uint8Array;
}
//# sourceMappingURL=index.d.ts.map