import { BufferDeserializer, NumberDeserializer, VectorDeserializer, BoolDeserializer, StringDeserializer, serializeBufferable, } from '../serialize/index.js';
import { Fr, Point, Buffer32 } from '../types/index.js';
export class BarretenbergApi {
    constructor(wasm) {
        this.wasm = wasm;
    }
    async pedersenCommit(inputsBuffer, ctxIndex) {
        const inArgs = [inputsBuffer, ctxIndex].map(serializeBufferable);
        const outTypes = [Point];
        const result = await this.wasm.callWasmExport('pedersen_commit', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    async pedersenHash(inputsBuffer, hashIndex) {
        const inArgs = [inputsBuffer, hashIndex].map(serializeBufferable);
        const outTypes = [Fr];
        const result = await this.wasm.callWasmExport('pedersen_hash', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    async pedersenHashes(inputsBuffer, hashIndex) {
        const inArgs = [inputsBuffer, hashIndex].map(serializeBufferable);
        const outTypes = [Fr];
        const result = await this.wasm.callWasmExport('pedersen_hashes', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    async pedersenHashBuffer(inputBuffer, hashIndex) {
        const inArgs = [inputBuffer, hashIndex].map(serializeBufferable);
        const outTypes = [Fr];
        const result = await this.wasm.callWasmExport('pedersen_hash_buffer', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    async poseidon2Hash(inputsBuffer) {
        const inArgs = [inputsBuffer].map(serializeBufferable);
        const outTypes = [Fr];
        const result = await this.wasm.callWasmExport('poseidon2_hash', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    async poseidon2Hashes(inputsBuffer) {
        const inArgs = [inputsBuffer].map(serializeBufferable);
        const outTypes = [Fr];
        const result = await this.wasm.callWasmExport('poseidon2_hashes', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    async poseidon2Permutation(inputsBuffer) {
        const inArgs = [inputsBuffer].map(serializeBufferable);
        const outTypes = [VectorDeserializer(Fr)];
        const result = await this.wasm.callWasmExport('poseidon2_permutation', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    async poseidon2HashAccumulate(inputsBuffer) {
        const inArgs = [inputsBuffer].map(serializeBufferable);
        const outTypes = [Fr];
        const result = await this.wasm.callWasmExport('poseidon2_hash_accumulate', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    async blake2s(data) {
        const inArgs = [data].map(serializeBufferable);
        const outTypes = [Buffer32];
        const result = await this.wasm.callWasmExport('blake2s', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    async blake2sToField(data) {
        const inArgs = [data].map(serializeBufferable);
        const outTypes = [Fr];
        const result = await this.wasm.callWasmExport('blake2s_to_field_', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    async aesEncryptBufferCbc(input, iv, key, length) {
        const inArgs = [input, iv, key, length].map(serializeBufferable);
        const outTypes = [BufferDeserializer()];
        const result = await this.wasm.callWasmExport('aes_encrypt_buffer_cbc', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    async aesDecryptBufferCbc(input, iv, key, length) {
        const inArgs = [input, iv, key, length].map(serializeBufferable);
        const outTypes = [BufferDeserializer()];
        const result = await this.wasm.callWasmExport('aes_decrypt_buffer_cbc', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    async srsInitSrs(pointsBuf, numPoints, g2PointBuf) {
        const inArgs = [pointsBuf, numPoints, g2PointBuf].map(serializeBufferable);
        const outTypes = [];
        const result = await this.wasm.callWasmExport('srs_init_srs', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return;
    }
    async srsInitGrumpkinSrs(pointsBuf, numPoints) {
        const inArgs = [pointsBuf, numPoints].map(serializeBufferable);
        const outTypes = [];
        const result = await this.wasm.callWasmExport('srs_init_grumpkin_srs', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return;
    }
    async testThreads(threads, iterations) {
        const inArgs = [threads, iterations].map(serializeBufferable);
        const outTypes = [NumberDeserializer()];
        const result = await this.wasm.callWasmExport('test_threads', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    async commonInitSlabAllocator(circuitSize) {
        const inArgs = [circuitSize].map(serializeBufferable);
        const outTypes = [];
        const result = await this.wasm.callWasmExport('common_init_slab_allocator', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return;
    }
    async acirGetCircuitSizes(constraintSystemBuf, recursive, honkRecursion) {
        const inArgs = [constraintSystemBuf, recursive, honkRecursion].map(serializeBufferable);
        const outTypes = [NumberDeserializer(), NumberDeserializer()];
        const result = await this.wasm.callWasmExport('acir_get_circuit_sizes', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out;
    }
    async acirProveAndVerifyUltraHonk(constraintSystemBuf, witnessBuf) {
        const inArgs = [constraintSystemBuf, witnessBuf].map(serializeBufferable);
        const outTypes = [BoolDeserializer()];
        const result = await this.wasm.callWasmExport('acir_prove_and_verify_ultra_honk', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    async acirProveAndVerifyMegaHonk(constraintSystemBuf, witnessBuf) {
        const inArgs = [constraintSystemBuf, witnessBuf].map(serializeBufferable);
        const outTypes = [BoolDeserializer()];
        const result = await this.wasm.callWasmExport('acir_prove_and_verify_mega_honk', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    async acirProveAztecClient(ivcInputsBuf) {
        const inArgs = [ivcInputsBuf].map(serializeBufferable);
        const outTypes = [BufferDeserializer(), BufferDeserializer()];
        const result = await this.wasm.callWasmExport('acir_prove_aztec_client', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out;
    }
    async acirVerifyAztecClient(proofBuf, vkBuf) {
        const inArgs = [proofBuf, vkBuf].map(serializeBufferable);
        const outTypes = [BoolDeserializer()];
        const result = await this.wasm.callWasmExport('acir_verify_aztec_client', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    async acirLoadVerificationKey(acirComposerPtr, vkBuf) {
        const inArgs = [acirComposerPtr, vkBuf].map(serializeBufferable);
        const outTypes = [];
        const result = await this.wasm.callWasmExport('acir_load_verification_key', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return;
    }
    async acirInitVerificationKey(acirComposerPtr) {
        const inArgs = [acirComposerPtr].map(serializeBufferable);
        const outTypes = [];
        const result = await this.wasm.callWasmExport('acir_init_verification_key', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return;
    }
    async acirGetVerificationKey(acirComposerPtr) {
        const inArgs = [acirComposerPtr].map(serializeBufferable);
        const outTypes = [BufferDeserializer()];
        const result = await this.wasm.callWasmExport('acir_get_verification_key', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    async acirGetProvingKey(acirComposerPtr, acirVec, recursive) {
        const inArgs = [acirComposerPtr, acirVec, recursive].map(serializeBufferable);
        const outTypes = [BufferDeserializer()];
        const result = await this.wasm.callWasmExport('acir_get_proving_key', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    async acirVerifyProof(acirComposerPtr, proofBuf) {
        const inArgs = [acirComposerPtr, proofBuf].map(serializeBufferable);
        const outTypes = [BoolDeserializer()];
        const result = await this.wasm.callWasmExport('acir_verify_proof', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    async acirGetSolidityVerifier(acirComposerPtr) {
        const inArgs = [acirComposerPtr].map(serializeBufferable);
        const outTypes = [StringDeserializer()];
        const result = await this.wasm.callWasmExport('acir_get_solidity_verifier', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    async acirHonkSolidityVerifier(proofBuf, vkBuf) {
        const inArgs = [proofBuf, vkBuf].map(serializeBufferable);
        const outTypes = [StringDeserializer()];
        const result = await this.wasm.callWasmExport('acir_honk_solidity_verifier', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    async acirSerializeProofIntoFields(acirComposerPtr, proofBuf, numInnerPublicInputs) {
        const inArgs = [acirComposerPtr, proofBuf, numInnerPublicInputs].map(serializeBufferable);
        const outTypes = [VectorDeserializer(Fr)];
        const result = await this.wasm.callWasmExport('acir_serialize_proof_into_fields', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    async acirSerializeVerificationKeyIntoFields(acirComposerPtr) {
        const inArgs = [acirComposerPtr].map(serializeBufferable);
        const outTypes = [VectorDeserializer(Fr), Fr];
        const result = await this.wasm.callWasmExport('acir_serialize_verification_key_into_fields', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out;
    }
    async acirProveUltraHonk(acirVec, witnessVec) {
        const inArgs = [acirVec, witnessVec].map(serializeBufferable);
        const outTypes = [BufferDeserializer()];
        const result = await this.wasm.callWasmExport('acir_prove_ultra_honk', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    async acirProveUltraKeccakHonk(acirVec, witnessVec) {
        const inArgs = [acirVec, witnessVec].map(serializeBufferable);
        const outTypes = [BufferDeserializer()];
        const result = await this.wasm.callWasmExport('acir_prove_ultra_keccak_honk', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    async acirProveUltraKeccakZkHonk(acirVec, witnessVec) {
        const inArgs = [acirVec, witnessVec].map(serializeBufferable);
        const outTypes = [BufferDeserializer()];
        const result = await this.wasm.callWasmExport('acir_prove_ultra_keccak_zk_honk', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    async acirProveUltraStarknetHonk(acirVec, witnessVec) {
        const inArgs = [acirVec, witnessVec].map(serializeBufferable);
        const outTypes = [BufferDeserializer()];
        const result = await this.wasm.callWasmExport('acir_prove_ultra_starknet_honk', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    async acirProveUltraStarknetZkHonk(acirVec, witnessVec) {
        const inArgs = [acirVec, witnessVec].map(serializeBufferable);
        const outTypes = [BufferDeserializer()];
        const result = await this.wasm.callWasmExport('acir_prove_ultra_starknet_zk_honk', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    async acirVerifyUltraHonk(proofBuf, vkBuf) {
        const inArgs = [proofBuf, vkBuf].map(serializeBufferable);
        const outTypes = [BoolDeserializer()];
        const result = await this.wasm.callWasmExport('acir_verify_ultra_honk', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    async acirVerifyUltraKeccakHonk(proofBuf, vkBuf) {
        const inArgs = [proofBuf, vkBuf].map(serializeBufferable);
        const outTypes = [BoolDeserializer()];
        const result = await this.wasm.callWasmExport('acir_verify_ultra_keccak_honk', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    async acirVerifyUltraKeccakZkHonk(proofBuf, vkBuf) {
        const inArgs = [proofBuf, vkBuf].map(serializeBufferable);
        const outTypes = [BoolDeserializer()];
        const result = await this.wasm.callWasmExport('acir_verify_ultra_keccak_zk_honk', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    async acirVerifyUltraStarknetHonk(proofBuf, vkBuf) {
        const inArgs = [proofBuf, vkBuf].map(serializeBufferable);
        const outTypes = [BoolDeserializer()];
        const result = await this.wasm.callWasmExport('acir_verify_ultra_starknet_honk', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    async acirVerifyUltraStarknetZkHonk(proofBuf, vkBuf) {
        const inArgs = [proofBuf, vkBuf].map(serializeBufferable);
        const outTypes = [BoolDeserializer()];
        const result = await this.wasm.callWasmExport('acir_verify_ultra_starknet_zk_honk', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    async acirWriteVkUltraHonk(acirVec) {
        const inArgs = [acirVec].map(serializeBufferable);
        const outTypes = [BufferDeserializer()];
        const result = await this.wasm.callWasmExport('acir_write_vk_ultra_honk', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    async acirWriteVkUltraKeccakHonk(acirVec) {
        const inArgs = [acirVec].map(serializeBufferable);
        const outTypes = [BufferDeserializer()];
        const result = await this.wasm.callWasmExport('acir_write_vk_ultra_keccak_honk', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    async acirWriteVkUltraKeccakZkHonk(acirVec) {
        const inArgs = [acirVec].map(serializeBufferable);
        const outTypes = [BufferDeserializer()];
        const result = await this.wasm.callWasmExport('acir_write_vk_ultra_keccak_zk_honk', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    async acirWriteVkUltraStarknetHonk(acirVec) {
        const inArgs = [acirVec].map(serializeBufferable);
        const outTypes = [BufferDeserializer()];
        const result = await this.wasm.callWasmExport('acir_write_vk_ultra_starknet_honk', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    async acirWriteVkUltraStarknetZkHonk(acirVec) {
        const inArgs = [acirVec].map(serializeBufferable);
        const outTypes = [BufferDeserializer()];
        const result = await this.wasm.callWasmExport('acir_write_vk_ultra_starknet_zk_honk', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    async acirProofAsFieldsUltraHonk(proofBuf) {
        const inArgs = [proofBuf].map(serializeBufferable);
        const outTypes = [VectorDeserializer(Fr)];
        const result = await this.wasm.callWasmExport('acir_proof_as_fields_ultra_honk', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    async acirVkAsFieldsUltraHonk(vkBuf) {
        const inArgs = [vkBuf].map(serializeBufferable);
        const outTypes = [VectorDeserializer(Fr)];
        const result = await this.wasm.callWasmExport('acir_vk_as_fields_ultra_honk', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    async acirVkAsFieldsMegaHonk(vkBuf) {
        const inArgs = [vkBuf].map(serializeBufferable);
        const outTypes = [VectorDeserializer(Fr)];
        const result = await this.wasm.callWasmExport('acir_vk_as_fields_mega_honk', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    async acirGatesAztecClient(ivcInputsBuf) {
        const inArgs = [ivcInputsBuf].map(serializeBufferable);
        const outTypes = [BufferDeserializer()];
        const result = await this.wasm.callWasmExport('acir_gates_aztec_client', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
}
export class BarretenbergApiSync {
    constructor(wasm) {
        this.wasm = wasm;
    }
    pedersenCommit(inputsBuffer, ctxIndex) {
        const inArgs = [inputsBuffer, ctxIndex].map(serializeBufferable);
        const outTypes = [Point];
        const result = this.wasm.callWasmExport('pedersen_commit', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    pedersenHash(inputsBuffer, hashIndex) {
        const inArgs = [inputsBuffer, hashIndex].map(serializeBufferable);
        const outTypes = [Fr];
        const result = this.wasm.callWasmExport('pedersen_hash', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    pedersenHashes(inputsBuffer, hashIndex) {
        const inArgs = [inputsBuffer, hashIndex].map(serializeBufferable);
        const outTypes = [Fr];
        const result = this.wasm.callWasmExport('pedersen_hashes', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    pedersenHashBuffer(inputBuffer, hashIndex) {
        const inArgs = [inputBuffer, hashIndex].map(serializeBufferable);
        const outTypes = [Fr];
        const result = this.wasm.callWasmExport('pedersen_hash_buffer', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    poseidon2Hash(inputsBuffer) {
        const inArgs = [inputsBuffer].map(serializeBufferable);
        const outTypes = [Fr];
        const result = this.wasm.callWasmExport('poseidon2_hash', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    poseidon2Hashes(inputsBuffer) {
        const inArgs = [inputsBuffer].map(serializeBufferable);
        const outTypes = [Fr];
        const result = this.wasm.callWasmExport('poseidon2_hashes', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    poseidon2Permutation(inputsBuffer) {
        const inArgs = [inputsBuffer].map(serializeBufferable);
        const outTypes = [VectorDeserializer(Fr)];
        const result = this.wasm.callWasmExport('poseidon2_permutation', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    poseidon2HashAccumulate(inputsBuffer) {
        const inArgs = [inputsBuffer].map(serializeBufferable);
        const outTypes = [Fr];
        const result = this.wasm.callWasmExport('poseidon2_hash_accumulate', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    blake2s(data) {
        const inArgs = [data].map(serializeBufferable);
        const outTypes = [Buffer32];
        const result = this.wasm.callWasmExport('blake2s', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    blake2sToField(data) {
        const inArgs = [data].map(serializeBufferable);
        const outTypes = [Fr];
        const result = this.wasm.callWasmExport('blake2s_to_field_', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    aesEncryptBufferCbc(input, iv, key, length) {
        const inArgs = [input, iv, key, length].map(serializeBufferable);
        const outTypes = [BufferDeserializer()];
        const result = this.wasm.callWasmExport('aes_encrypt_buffer_cbc', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    aesDecryptBufferCbc(input, iv, key, length) {
        const inArgs = [input, iv, key, length].map(serializeBufferable);
        const outTypes = [BufferDeserializer()];
        const result = this.wasm.callWasmExport('aes_decrypt_buffer_cbc', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    srsInitSrs(pointsBuf, numPoints, g2PointBuf) {
        const inArgs = [pointsBuf, numPoints, g2PointBuf].map(serializeBufferable);
        const outTypes = [];
        const result = this.wasm.callWasmExport('srs_init_srs', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return;
    }
    srsInitGrumpkinSrs(pointsBuf, numPoints) {
        const inArgs = [pointsBuf, numPoints].map(serializeBufferable);
        const outTypes = [];
        const result = this.wasm.callWasmExport('srs_init_grumpkin_srs', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return;
    }
    testThreads(threads, iterations) {
        const inArgs = [threads, iterations].map(serializeBufferable);
        const outTypes = [NumberDeserializer()];
        const result = this.wasm.callWasmExport('test_threads', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    commonInitSlabAllocator(circuitSize) {
        const inArgs = [circuitSize].map(serializeBufferable);
        const outTypes = [];
        const result = this.wasm.callWasmExport('common_init_slab_allocator', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return;
    }
    acirGetCircuitSizes(constraintSystemBuf, recursive, honkRecursion) {
        const inArgs = [constraintSystemBuf, recursive, honkRecursion].map(serializeBufferable);
        const outTypes = [NumberDeserializer(), NumberDeserializer()];
        const result = this.wasm.callWasmExport('acir_get_circuit_sizes', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out;
    }
    acirProveAndVerifyUltraHonk(constraintSystemBuf, witnessBuf) {
        const inArgs = [constraintSystemBuf, witnessBuf].map(serializeBufferable);
        const outTypes = [BoolDeserializer()];
        const result = this.wasm.callWasmExport('acir_prove_and_verify_ultra_honk', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    acirProveAndVerifyMegaHonk(constraintSystemBuf, witnessBuf) {
        const inArgs = [constraintSystemBuf, witnessBuf].map(serializeBufferable);
        const outTypes = [BoolDeserializer()];
        const result = this.wasm.callWasmExport('acir_prove_and_verify_mega_honk', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    acirProveAztecClient(ivcInputsBuf) {
        const inArgs = [ivcInputsBuf].map(serializeBufferable);
        const outTypes = [BufferDeserializer(), BufferDeserializer()];
        const result = this.wasm.callWasmExport('acir_prove_aztec_client', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out;
    }
    acirVerifyAztecClient(proofBuf, vkBuf) {
        const inArgs = [proofBuf, vkBuf].map(serializeBufferable);
        const outTypes = [BoolDeserializer()];
        const result = this.wasm.callWasmExport('acir_verify_aztec_client', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    acirLoadVerificationKey(acirComposerPtr, vkBuf) {
        const inArgs = [acirComposerPtr, vkBuf].map(serializeBufferable);
        const outTypes = [];
        const result = this.wasm.callWasmExport('acir_load_verification_key', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return;
    }
    acirInitVerificationKey(acirComposerPtr) {
        const inArgs = [acirComposerPtr].map(serializeBufferable);
        const outTypes = [];
        const result = this.wasm.callWasmExport('acir_init_verification_key', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return;
    }
    acirGetVerificationKey(acirComposerPtr) {
        const inArgs = [acirComposerPtr].map(serializeBufferable);
        const outTypes = [BufferDeserializer()];
        const result = this.wasm.callWasmExport('acir_get_verification_key', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    acirGetProvingKey(acirComposerPtr, acirVec, recursive) {
        const inArgs = [acirComposerPtr, acirVec, recursive].map(serializeBufferable);
        const outTypes = [BufferDeserializer()];
        const result = this.wasm.callWasmExport('acir_get_proving_key', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    acirVerifyProof(acirComposerPtr, proofBuf) {
        const inArgs = [acirComposerPtr, proofBuf].map(serializeBufferable);
        const outTypes = [BoolDeserializer()];
        const result = this.wasm.callWasmExport('acir_verify_proof', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    acirGetSolidityVerifier(acirComposerPtr) {
        const inArgs = [acirComposerPtr].map(serializeBufferable);
        const outTypes = [StringDeserializer()];
        const result = this.wasm.callWasmExport('acir_get_solidity_verifier', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    acirHonkSolidityVerifier(proofBuf, vkBuf) {
        const inArgs = [proofBuf, vkBuf].map(serializeBufferable);
        const outTypes = [StringDeserializer()];
        const result = this.wasm.callWasmExport('acir_honk_solidity_verifier', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    acirSerializeProofIntoFields(acirComposerPtr, proofBuf, numInnerPublicInputs) {
        const inArgs = [acirComposerPtr, proofBuf, numInnerPublicInputs].map(serializeBufferable);
        const outTypes = [VectorDeserializer(Fr)];
        const result = this.wasm.callWasmExport('acir_serialize_proof_into_fields', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    acirSerializeVerificationKeyIntoFields(acirComposerPtr) {
        const inArgs = [acirComposerPtr].map(serializeBufferable);
        const outTypes = [VectorDeserializer(Fr), Fr];
        const result = this.wasm.callWasmExport('acir_serialize_verification_key_into_fields', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out;
    }
    acirProveUltraHonk(acirVec, witnessVec) {
        const inArgs = [acirVec, witnessVec].map(serializeBufferable);
        const outTypes = [BufferDeserializer()];
        const result = this.wasm.callWasmExport('acir_prove_ultra_honk', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    acirProveUltraKeccakHonk(acirVec, witnessVec) {
        const inArgs = [acirVec, witnessVec].map(serializeBufferable);
        const outTypes = [BufferDeserializer()];
        const result = this.wasm.callWasmExport('acir_prove_ultra_keccak_honk', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    acirProveUltraKeccakZkHonk(acirVec, witnessVec) {
        const inArgs = [acirVec, witnessVec].map(serializeBufferable);
        const outTypes = [BufferDeserializer()];
        const result = this.wasm.callWasmExport('acir_prove_ultra_keccak_zk_honk', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    acirProveUltraStarknetHonk(acirVec, witnessVec) {
        const inArgs = [acirVec, witnessVec].map(serializeBufferable);
        const outTypes = [BufferDeserializer()];
        const result = this.wasm.callWasmExport('acir_prove_ultra_starknet_honk', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    acirProveUltraStarknetZkHonk(acirVec, witnessVec) {
        const inArgs = [acirVec, witnessVec].map(serializeBufferable);
        const outTypes = [BufferDeserializer()];
        const result = this.wasm.callWasmExport('acir_prove_ultra_starknet_zk_honk', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    acirVerifyUltraHonk(proofBuf, vkBuf) {
        const inArgs = [proofBuf, vkBuf].map(serializeBufferable);
        const outTypes = [BoolDeserializer()];
        const result = this.wasm.callWasmExport('acir_verify_ultra_honk', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    acirVerifyUltraKeccakHonk(proofBuf, vkBuf) {
        const inArgs = [proofBuf, vkBuf].map(serializeBufferable);
        const outTypes = [BoolDeserializer()];
        const result = this.wasm.callWasmExport('acir_verify_ultra_keccak_honk', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    acirVerifyUltraKeccakZkHonk(proofBuf, vkBuf) {
        const inArgs = [proofBuf, vkBuf].map(serializeBufferable);
        const outTypes = [BoolDeserializer()];
        const result = this.wasm.callWasmExport('acir_verify_ultra_keccak_zk_honk', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    acirVerifyUltraStarknetHonk(proofBuf, vkBuf) {
        const inArgs = [proofBuf, vkBuf].map(serializeBufferable);
        const outTypes = [BoolDeserializer()];
        const result = this.wasm.callWasmExport('acir_verify_ultra_starknet_honk', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    acirVerifyUltraStarknetZkHonk(proofBuf, vkBuf) {
        const inArgs = [proofBuf, vkBuf].map(serializeBufferable);
        const outTypes = [BoolDeserializer()];
        const result = this.wasm.callWasmExport('acir_verify_ultra_starknet_zk_honk', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    acirWriteVkUltraHonk(acirVec) {
        const inArgs = [acirVec].map(serializeBufferable);
        const outTypes = [BufferDeserializer()];
        const result = this.wasm.callWasmExport('acir_write_vk_ultra_honk', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    acirWriteVkUltraKeccakHonk(acirVec) {
        const inArgs = [acirVec].map(serializeBufferable);
        const outTypes = [BufferDeserializer()];
        const result = this.wasm.callWasmExport('acir_write_vk_ultra_keccak_honk', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    acirWriteVkUltraKeccakZkHonk(acirVec) {
        const inArgs = [acirVec].map(serializeBufferable);
        const outTypes = [BufferDeserializer()];
        const result = this.wasm.callWasmExport('acir_write_vk_ultra_keccak_zk_honk', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    acirWriteVkUltraStarknetHonk(acirVec) {
        const inArgs = [acirVec].map(serializeBufferable);
        const outTypes = [BufferDeserializer()];
        const result = this.wasm.callWasmExport('acir_write_vk_ultra_starknet_honk', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    acirWriteVkUltraStarknetZkHonk(acirVec) {
        const inArgs = [acirVec].map(serializeBufferable);
        const outTypes = [BufferDeserializer()];
        const result = this.wasm.callWasmExport('acir_write_vk_ultra_starknet_zk_honk', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    acirProofAsFieldsUltraHonk(proofBuf) {
        const inArgs = [proofBuf].map(serializeBufferable);
        const outTypes = [VectorDeserializer(Fr)];
        const result = this.wasm.callWasmExport('acir_proof_as_fields_ultra_honk', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    acirVkAsFieldsUltraHonk(vkBuf) {
        const inArgs = [vkBuf].map(serializeBufferable);
        const outTypes = [VectorDeserializer(Fr)];
        const result = this.wasm.callWasmExport('acir_vk_as_fields_ultra_honk', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    acirVkAsFieldsMegaHonk(vkBuf) {
        const inArgs = [vkBuf].map(serializeBufferable);
        const outTypes = [VectorDeserializer(Fr)];
        const result = this.wasm.callWasmExport('acir_vk_as_fields_mega_honk', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
    acirGatesAztecClient(ivcInputsBuf) {
        const inArgs = [ivcInputsBuf].map(serializeBufferable);
        const outTypes = [BufferDeserializer()];
        const result = this.wasm.callWasmExport('acir_gates_aztec_client', inArgs, outTypes.map(t => t.SIZE_IN_BYTES));
        const out = result.map((r, i) => outTypes[i].fromBuffer(r));
        return out[0];
    }
}
//# sourceMappingURL=data:application/json;base64,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