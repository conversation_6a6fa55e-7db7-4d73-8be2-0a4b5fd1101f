import { readFile } from 'fs/promises';
import { dirname } from 'path';
import { fileURLToPath } from 'url';
import pako from 'pako';
function getCurrentDir() {
    if (typeof __dirname !== 'undefined') {
        return __dirname;
    }
    else {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        return dirname(fileURLToPath(import.meta.url));
    }
}
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export async function fetchCode(multithreaded, wasmPath) {
    const path = wasmPath ?? getCurrentDir() + '/../../barretenberg-threads.wasm.gz';
    // Default bb wasm is compressed, but user could point it to a non-compressed version
    const maybeCompressedData = await readFile(path);
    const buffer = new Uint8Array(maybeCompressedData);
    const isGzip = 
    // Check magic number
    buffer[0] === 0x1f &&
        buffer[1] === 0x8b &&
        // Check compression method:
        buffer[2] === 0x08;
    if (isGzip) {
        const decompressedData = pako.ungzip(buffer);
        return decompressedData.buffer;
    }
    else {
        return buffer;
    }
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaW5kZXguanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi9zcmMvYmFycmV0ZW5iZXJnX3dhc20vZmV0Y2hfY29kZS9ub2RlL2luZGV4LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLE9BQU8sRUFBRSxRQUFRLEVBQUUsTUFBTSxhQUFhLENBQUM7QUFDdkMsT0FBTyxFQUFFLE9BQU8sRUFBRSxNQUFNLE1BQU0sQ0FBQztBQUMvQixPQUFPLEVBQUUsYUFBYSxFQUFFLE1BQU0sS0FBSyxDQUFDO0FBQ3BDLE9BQU8sSUFBSSxNQUFNLE1BQU0sQ0FBQztBQUV4QixTQUFTLGFBQWE7SUFDcEIsSUFBSSxPQUFPLFNBQVMsS0FBSyxXQUFXLEVBQUUsQ0FBQztRQUNyQyxPQUFPLFNBQVMsQ0FBQztJQUNuQixDQUFDO1NBQU0sQ0FBQztRQUNOLDZEQUE2RDtRQUM3RCxhQUFhO1FBQ2IsT0FBTyxPQUFPLENBQUMsYUFBYSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQztJQUNqRCxDQUFDO0FBQ0gsQ0FBQztBQUVELDZEQUE2RDtBQUM3RCxNQUFNLENBQUMsS0FBSyxVQUFVLFNBQVMsQ0FBQyxhQUFzQixFQUFFLFFBQWlCO0lBQ3ZFLE1BQU0sSUFBSSxHQUFHLFFBQVEsSUFBSSxhQUFhLEVBQUUsR0FBRyxxQ0FBcUMsQ0FBQztJQUNqRixxRkFBcUY7SUFDckYsTUFBTSxtQkFBbUIsR0FBRyxNQUFNLFFBQVEsQ0FBQyxJQUFJLENBQUMsQ0FBQztJQUNqRCxNQUFNLE1BQU0sR0FBRyxJQUFJLFVBQVUsQ0FBQyxtQkFBbUIsQ0FBQyxDQUFDO0lBQ25ELE1BQU0sTUFBTTtJQUNWLHFCQUFxQjtJQUNyQixNQUFNLENBQUMsQ0FBQyxDQUFDLEtBQUssSUFBSTtRQUNsQixNQUFNLENBQUMsQ0FBQyxDQUFDLEtBQUssSUFBSTtRQUNsQiw0QkFBNEI7UUFDNUIsTUFBTSxDQUFDLENBQUMsQ0FBQyxLQUFLLElBQUksQ0FBQztJQUNyQixJQUFJLE1BQU0sRUFBRSxDQUFDO1FBQ1gsTUFBTSxnQkFBZ0IsR0FBRyxJQUFJLENBQUMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxDQUFDO1FBQzdDLE9BQU8sZ0JBQWdCLENBQUMsTUFBNEMsQ0FBQztJQUN2RSxDQUFDO1NBQU0sQ0FBQztRQUNOLE9BQU8sTUFBTSxDQUFDO0lBQ2hCLENBQUM7QUFDSCxDQUFDIn0=