/**
 * Convert a 32-byte BE Buffer to a BigInt.
 */
export declare function buffer32BytesToBigIntBE(buf: Buffer): bigint;
/**
 * Convert a BE Uint8Array to a BigInt.
 */
export declare function uint8ArrayToBigIntBE(bytes: Uint8Array): bigint;
/**
 * Convert a BigInt to a 32-byte BE Buffer.
 */
export declare function bigIntToBufferBE(value: bigint, byteLength?: number): Buffer;
/**
 * Convert a BigInt to a 32-byte BE Uint8Array.
 */
export declare function bigIntToUint8ArrayBE(value: bigint, byteLength?: number): Uint8Array;
//# sourceMappingURL=index.d.ts.map