import fs from 'fs';
import { mapDeserializer, mapRustType } from './mappings.js';
export function generateRustCode(filename) {
    const fileContent = fs.readFileSync(filename, 'utf-8');
    const functionDeclarations = JSON.parse(fileContent);
    let output = `
// WARNING: FILE CODE GENERATED BY BINDGEN UTILITY. DO NOT EDIT!
use crate::call_wasm_export::call_wasm_export;
use crate::serialize::{BufferDeserializer, NumberDeserializer, VectorDeserializer, BoolDeserializer};
use crate::types::{Fr, Fq, Point, Buffer32, Buffer128};
`;
    for (const { functionName, inArgs, outArgs } of functionDeclarations) {
        const parameters = inArgs.map(({ name, type }) => `${name}: ${mapRustType(type)}`).join(', ');
        const inArgsVar = `let in_args = vec![${inArgs.map(arg => arg.name).join(', ')}];`;
        const outTypesVar = `let out_types = vec![${outArgs.map(arg => mapDeserializer(arg.type)).join(', ')}];`;
        const wasmCall = `let result = call_wasm_export(&"${functionName}", &in_args, &out_types)?;`;
        const returnStmt = getReturnStmt(outArgs);
        const returnType = outArgs.length === 0
            ? '-> Result<(), Box<dyn std::error::Error>>'
            : `-> Result<(${outArgs.map(a => mapRustType(a.type)).join(', ')}), Box<dyn std::error::Error>>`;
        const functionDecl = `
pub fn ${functionName}(${parameters})${returnType} {
    ${inArgsVar}
    ${outTypesVar}
    ${wasmCall}
    ${returnStmt}
}
`;
        output += functionDecl;
    }
    return output;
}
function getReturnStmt(outArgs) {
    switch (outArgs.length) {
        case 0:
            return 'Ok(())';
        case 1:
            return `Ok(result[0].clone())`;
        default:
            return `Ok((${outArgs.map((_, idx) => `result[${idx}].clone()`).join(', ')}))`;
    }
}
//# sourceMappingURL=data:application/json;base64,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