import fs from 'fs';
import { mapDeserializer, mapType } from './mappings.js';
import { toCamelCase } from './to_camel_case.js';
export function generateTypeScriptCode(filename) {
    const fileContent = fs.readFileSync(filename, 'utf-8');
    const functionDeclarations = JSON.parse(fileContent);
    let output = `// WARNING: FILE CODE GENERATED BY BINDGEN UTILITY. DO NOT EDIT!
/* eslint-disable @typescript-eslint/no-unused-vars */
import { BarretenbergWasmMain, BarretenbergWasmMainWorker } from '../barretenberg_wasm/barretenberg_wasm_main/index.js';
import { BufferDeserializer, NumberDeserializer, VectorDeserializer, BoolDeserializer, StringDeserializer, serializeBufferable, OutputType } from '../serialize/index.js';
import { Fr, Point, Buffer32, Ptr } from '../types/index.js';

`;
    output += generateClass(functionDeclarations);
    output += generateSyncClass(functionDeclarations);
    return output;
}
function generateClass(functionDeclarations) {
    let output = `
export class BarretenbergApi {
  constructor(protected wasm: BarretenbergWasmMainWorker) {}

`;
    for (const { functionName, inArgs, outArgs } of functionDeclarations) {
        try {
            const parameters = inArgs.map(({ name, type }) => `${toCamelCase(name)}: ${mapType(type)}`).join(', ');
            const inArgsVar = `const inArgs = [${inArgs
                .map(arg => toCamelCase(arg.name))
                .join(', ')}].map(serializeBufferable);`;
            const outTypesVar = `const outTypes: OutputType[] = [${outArgs
                .map(arg => mapDeserializer(arg.type))
                .join(', ')}];`;
            const wasmCall = `const result = await this.wasm.callWasmExport('${functionName}', inArgs, outTypes.map(t=>t.SIZE_IN_BYTES));`;
            const outVar = `const out = result.map((r, i) => outTypes[i].fromBuffer(r));`;
            const n = outArgs.length;
            const returnStmt = n === 0 ? 'return;' : n === 1 ? 'return out[0];' : 'return out as any;';
            const returnType = outArgs.length === 0
                ? 'void'
                : outArgs.length === 1
                    ? `${mapType(outArgs[0].type)}`
                    : `[${outArgs.map(a => mapType(a.type)).join(', ')}]`;
            output += `
  async ${toCamelCase(functionName)}(${parameters}): Promise<${returnType}> {
    ${inArgsVar}
    ${outTypesVar}
    ${wasmCall}
    ${outVar}
    ${returnStmt}
  }
`;
        }
        catch (err) {
            throw new Error(`Function ${functionName}: ${err.message}`);
        }
    }
    output += `}`;
    return output;
}
function generateSyncClass(functionDeclarations) {
    let output = `
export class BarretenbergApiSync {
  constructor(protected wasm: BarretenbergWasmMain) {}

`;
    for (const { functionName, inArgs, outArgs } of functionDeclarations) {
        try {
            const parameters = inArgs.map(({ name, type }) => `${toCamelCase(name)}: ${mapType(type)}`).join(', ');
            const inArgsVar = `const inArgs = [${inArgs
                .map(arg => toCamelCase(arg.name))
                .join(', ')}].map(serializeBufferable);`;
            const outTypesVar = `const outTypes: OutputType[] = [${outArgs
                .map(arg => mapDeserializer(arg.type))
                .join(', ')}];`;
            const wasmCall = `const result = this.wasm.callWasmExport('${functionName}', inArgs, outTypes.map(t=>t.SIZE_IN_BYTES));`;
            const outVar = `const out = result.map((r, i) => outTypes[i].fromBuffer(r));`;
            const n = outArgs.length;
            const returnStmt = n === 0 ? 'return;' : n === 1 ? 'return out[0];' : 'return out as any;';
            const returnType = outArgs.length === 0
                ? 'void'
                : outArgs.length === 1
                    ? `${mapType(outArgs[0].type)}`
                    : `[${outArgs.map(a => mapType(a.type)).join(', ')}]`;
            output += `
  ${toCamelCase(functionName)}(${parameters}): ${returnType} {
    ${inArgsVar}
    ${outTypesVar}
    ${wasmCall}
    ${outVar}
    ${returnStmt}
  }
`;
        }
        catch (err) {
            throw new Error(`Function ${functionName}: ${err.message}`);
        }
    }
    output += `}`;
    return output;
}
//# sourceMappingURL=data:application/json;base64,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