var _a, _b;
import { randomBytes } from '../random/index.js';
import { buffer32BytesToBigIntBE, uint8ArrayToBigIntBE, bigIntToBufferBE, bigIntToUint8ArrayBE, } from '../bigint-array/index.js';
import { BufferReader, uint8ArrayToHexString } from '../serialize/index.js';
// TODO(#4189): Replace with implementation in yarn-project/foundation/src/fields/fields.ts
/**
 * Fr field class.
 * @dev This class is used to represent elements of BN254 scalar field or elements in the base field of Grumpkin.
 * (Grumpkin's scalar field corresponds to BN254's base field and vice versa.)
 */
export class Fr {
    constructor(value) {
        // We convert buffer value to bigint to be able to check it fits within modulus
        const valueBigInt = typeof value === 'bigint'
            ? value
            : value instanceof Buffer
                ? buffer32BytesToBigIntBE(value)
                : uint8ArrayToBigIntBE(value);
        if (valueBigInt > _a.MAX_VALUE) {
            throw new Error(`Value 0x${valueBigInt.toString(16)} is greater or equal to field modulus.`);
        }
        this.value =
            typeof value === 'bigint' ? bigIntToUint8ArrayBE(value) : value instanceof Buffer ? new Uint8Array(value) : value;
    }
    static random() {
        const r = uint8ArrayToBigIntBE(randomBytes(64)) % _a.MODULUS;
        return new this(r);
    }
    static fromBuffer(buffer) {
        const reader = BufferReader.asReader(buffer);
        return new this(reader.readBytes(this.SIZE_IN_BYTES));
    }
    static fromBufferReduce(buffer) {
        const reader = BufferReader.asReader(buffer);
        return new this(uint8ArrayToBigIntBE(reader.readBytes(this.SIZE_IN_BYTES)) % _a.MODULUS);
    }
    static fromString(str) {
        return this.fromBuffer(Buffer.from(str.replace(/^0x/i, ''), 'hex'));
    }
    toBuffer() {
        return this.value;
    }
    toString() {
        return '0x' + uint8ArrayToHexString(this.toBuffer());
    }
    equals(rhs) {
        return this.value.every((v, i) => v === rhs.value[i]);
    }
    isZero() {
        return this.value.every(v => v === 0);
    }
}
_a = Fr;
Fr.ZERO = new _a(0n);
Fr.MODULUS = 0x30644e72e131a029b85045b68181585d2833e84879b9709143e1f593f0000001n;
Fr.MAX_VALUE = _a.MODULUS - 1n;
Fr.SIZE_IN_BYTES = 32;
/**
 * Fq field class.
 * @dev This class is used to represent elements of BN254 base field or elements in the scalar field of Grumpkin.
 * (Grumpkin's scalar field corresponds to BN254's base field and vice versa.)
 */
export class Fq {
    constructor(value) {
        this.value = value;
        if (value > _b.MAX_VALUE) {
            throw new Error(`Fq out of range ${value}.`);
        }
    }
    static random() {
        const r = uint8ArrayToBigIntBE(randomBytes(64)) % _b.MODULUS;
        return new this(r);
    }
    static fromBuffer(buffer) {
        const reader = BufferReader.asReader(buffer);
        return new this(uint8ArrayToBigIntBE(reader.readBytes(this.SIZE_IN_BYTES)));
    }
    static fromBufferReduce(buffer) {
        const reader = BufferReader.asReader(buffer);
        return new this(uint8ArrayToBigIntBE(reader.readBytes(this.SIZE_IN_BYTES)) % Fr.MODULUS);
    }
    static fromString(str) {
        return this.fromBuffer(Buffer.from(str.replace(/^0x/i, ''), 'hex'));
    }
    toBuffer() {
        return bigIntToBufferBE(this.value, _b.SIZE_IN_BYTES);
    }
    toString() {
        return '0x' + this.value.toString(16);
    }
    equals(rhs) {
        return this.value === rhs.value;
    }
    isZero() {
        return this.value === 0n;
    }
}
_b = Fq;
Fq.MODULUS = 0x30644e72e131a029b85045b68181585d97816a916871ca8d3c208c16d87cfd47n;
Fq.MAX_VALUE = _b.MODULUS - 1n;
Fq.SIZE_IN_BYTES = 32;
//# sourceMappingURL=data:application/json;base64,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