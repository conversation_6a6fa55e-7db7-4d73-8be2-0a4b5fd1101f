import { Fr } from './index.js';
import { <PERSON><PERSON><PERSON><PERSON>ead<PERSON> } from '../serialize/buffer_reader.js';
export declare class Point {
    readonly x: Fr;
    readonly y: Fr;
    static SIZE_IN_BYTES: number;
    static EMPTY: Point;
    constructor(x: Fr, y: Fr);
    static random(): Point;
    static fromBuffer(buffer: Uint8Array | BufferReader): Point;
    static fromString(address: string): Point;
    toBuffer(): Buffer<ArrayBuffer>;
    toString(): string;
    equals(rhs: Point): boolean;
}
//# sourceMappingURL=point.d.ts.map