{"name": "@aztec/bb.js", "packageManager": "yarn@4.5.2", "version": "0.87.3", "homepage": "https://github.com/AztecProtocol/aztec-packages/tree/master/barretenberg/ts", "license": "MIT", "type": "module", "types": "./dest/node/index.d.ts", "exports": {".": {"require": "./dest/node-cjs/index.js", "browser": "./dest/browser/index.js", "default": "./dest/node/index.js"}}, "bin": "dest/node/main.js", "files": ["src/", "dest/", "cjs-entry/", "README.md"], "scripts": {"clean": "rm -rf ./dest .tsbuildinfo .tsbuildinfo.cjs", "build": "yarn clean && yarn build:wasm && yarn build:esm && yarn build:cjs && yarn build:browser", "build:wasm": "./scripts/build_wasm.sh", "build:esm": "tsc -b tsconfig.esm.json && chmod +x ./dest/node/main.js", "build:cjs": "tsc -b tsconfig.cjs.json && ./scripts/cjs_postprocess.sh", "build:browser": "tsc -b tsconfig.browser.json && ./scripts/browser_postprocess.sh", "build:bindings": "cd .. && ./scripts/bindgen.sh", "formatting": "prettier --check ./src && eslint --max-warnings 0 ./src", "formatting:fix": "prettier -w ./src", "test": "NODE_OPTIONS='--loader ts-node/esm' NODE_NO_WARNINGS=1 node --experimental-vm-modules $(yarn bin jest) --no-cache --passWithNoTests", "test:debug": "NODE_NO_WARNINGS=1 node --inspect-brk=0.0.0.0 --experimental-vm-modules ./node_modules/.bin/jest --no-cache --passWithNoTests --runInBand", "simple_test": "NODE_NO_WARNINGS=1 node ./src/examples/simple.rawtest.ts", "deploy": "npm publish --access public"}, "jest": {"preset": "ts-jest/presets/default-esm", "transform": {"^.+\\.tsx?$": ["@swc/jest", {"jsc": {"parser": {"syntax": "typescript", "decorators": true}, "transform": {"decoratorVersion": "2022-03"}}}]}, "moduleNameMapper": {"^(\\.{1,2}/.*)\\.js$": "$1"}, "extensionsToTreatAsEsm": [".ts"], "testRegex": "./src/.*\\.test\\.ts$", "rootDir": "./src"}, "dependencies": {"comlink": "^4.4.1", "commander": "^12.1.0", "idb-keyval": "^6.2.1", "msgpackr": "^1.11.2", "pako": "^2.1.0", "pino": "^9.5.0", "tslib": "^2.4.0"}, "devDependencies": {"@jest/globals": "^29.4.3", "@swc/core": "^1.10.1", "@swc/jest": "^0.2.37", "@types/detect-node": "^2.0.0", "@types/jest": "^29.4.0", "@types/node": "^22.15.17", "@types/pako": "^2.0.3", "@types/source-map-support": "^0.5.6", "eslint": "^9.26.0", "eslint-config-prettier": "^10.1.5", "jest": "^29.5.0", "prettier": "^3.5.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.2", "ts-node": "^10.9.1", "typescript": "^5.3.3", "typescript-eslint": "^8.32.1"}}