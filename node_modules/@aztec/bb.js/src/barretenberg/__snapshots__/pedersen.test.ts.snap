// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`pedersen sync pedersenCommit 1`] = `
Point {
  "x": Fr {
    "value": Uint8Array [
      40,
      159,
      125,
      144,
      234,
      153,
      219,
      166,
      76,
      75,
      47,
      51,
      253,
      27,
      9,
      101,
      2,
      145,
      223,
      38,
      43,
      114,
      5,
      21,
      90,
      97,
      2,
      6,
      219,
      97,
      109,
      152,
    ],
  },
  "y": Fr {
    "value": Uint8Array [
      5,
      175,
      199,
      200,
      35,
      67,
      88,
      76,
      19,
      203,
      45,
      50,
      137,
      153,
      67,
      200,
      57,
      87,
      22,
      209,
      141,
      173,
      205,
      189,
      23,
      215,
      206,
      3,
      174,
      112,
      128,
      11,
    ],
  },
}
`;

exports[`pedersen sync pedersenHash 1`] = `
Fr {
  "value": Uint8Array [
    4,
    194,
    53,
    42,
    6,
    13,
    74,
    193,
    205,
    251,
    96,
    62,
    188,
    67,
    39,
    181,
    118,
    69,
    151,
    35,
    22,
    20,
    246,
    29,
    36,
    91,
    243,
    87,
    114,
    192,
    134,
    150,
  ],
}
`;

exports[`pedersen sync pedersenHashBuffer 1`] = `
Fr {
  "value": Uint8Array [
    43,
    213,
    196,
    82,
    160,
    201,
    113,
    98,
    41,
    79,
    201,
    223,
    208,
    241,
    224,
    157,
    14,
    9,
    201,
    95,
    165,
    237,
    63,
    241,
    73,
    251,
    222,
    243,
    102,
    203,
    81,
    249,
  ],
}
`;
