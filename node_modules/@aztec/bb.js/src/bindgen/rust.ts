import fs from 'fs';
import { Arg, FunctionDeclaration } from './function_declaration.js';
import { mapDeserializer, mapRustType } from './mappings.js';

export function generateRustCode(filename: string) {
  const fileContent = fs.readFileSync(filename, 'utf-8');
  const functionDeclarations: FunctionDeclaration[] = JSON.parse(fileContent);

  let output = `
// WARNING: FILE CODE GENERATED BY BINDGEN UTILITY. DO NOT EDIT!
use crate::call_wasm_export::call_wasm_export;
use crate::serialize::{BufferDeserializer, NumberDeserializer, VectorDeserializer, BoolDeserializer};
use crate::types::{Fr, Fq, Point, Buffer32, Buffer128};
`;

  for (const { functionName, inArgs, outArgs } of functionDeclarations) {
    const parameters = inArgs.map(({ name, type }) => `${name}: ${mapRustType(type)}`).join(', ');
    const inArgsVar = `let in_args = vec![${inArgs.map(arg => arg.name).join(', ')}];`;
    const outTypesVar = `let out_types = vec![${outArgs.map(arg => mapDeserializer(arg.type)).join(', ')}];`;
    const wasmCall = `let result = call_wasm_export(&"${functionName}", &in_args, &out_types)?;`;

    const returnStmt = getReturnStmt(outArgs);
    const returnType =
      outArgs.length === 0
        ? '-> Result<(), Box<dyn std::error::Error>>'
        : `-> Result<(${outArgs.map(a => mapRustType(a.type)).join(', ')}), Box<dyn std::error::Error>>`;

    const functionDecl = `
pub fn ${functionName}(${parameters})${returnType} {
    ${inArgsVar}
    ${outTypesVar}
    ${wasmCall}
    ${returnStmt}
}
`;

    output += functionDecl;
  }

  return output;
}

function getReturnStmt(outArgs: Arg[]) {
  switch (outArgs.length) {
    case 0:
      return 'Ok(())';
    case 1:
      return `Ok(result[0].clone())`;
    default:
      return `Ok((${outArgs.map((_, idx) => `result[${idx}].clone()`).join(', ')}))`;
  }
}
