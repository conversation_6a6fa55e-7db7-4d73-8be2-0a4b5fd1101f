import fs from 'fs';
import { mapDeserializer, mapType } from './mappings.js';
import { toCamelCase } from './to_camel_case.js';
import { FunctionDeclaration } from './function_declaration.js';

export function generateTypeScriptCode(filename: string) {
  const fileContent = fs.readFileSync(filename, 'utf-8');
  const functionDeclarations: FunctionDeclaration[] = JSON.parse(fileContent);

  let output = `// WARNING: FILE CODE GENERATED BY BINDGEN UTILITY. DO NOT EDIT!
/* eslint-disable @typescript-eslint/no-unused-vars */
import { BarretenbergWasmMain, BarretenbergWasmMainWorker } from '../barretenberg_wasm/barretenberg_wasm_main/index.js';
import { BufferDeserializer, NumberDeserializer, VectorDeserializer, BoolDeserializer, StringDeserializer, serializeBufferable, OutputType } from '../serialize/index.js';
import { Fr, Point, Buffer32, Ptr } from '../types/index.js';

`;

  output += generateClass(functionDeclarations);
  output += generateSyncClass(functionDeclarations);

  return output;
}

function generateClass(functionDeclarations: FunctionDeclaration[]) {
  let output = `
export class BarretenbergApi {
  constructor(protected wasm: BarretenbergWasmMainWorker) {}

`;

  for (const { functionName, inArgs, outArgs } of functionDeclarations) {
    try {
      const parameters = inArgs.map(({ name, type }) => `${toCamelCase(name)}: ${mapType(type)}`).join(', ');
      const inArgsVar = `const inArgs = [${inArgs
        .map(arg => toCamelCase(arg.name))
        .join(', ')}].map(serializeBufferable);`;
      const outTypesVar = `const outTypes: OutputType[] = [${outArgs
        .map(arg => mapDeserializer(arg.type))
        .join(', ')}];`;
      const wasmCall = `const result = await this.wasm.callWasmExport('${functionName}', inArgs, outTypes.map(t=>t.SIZE_IN_BYTES));`;
      const outVar = `const out = result.map((r, i) => outTypes[i].fromBuffer(r));`;

      const n = outArgs.length;
      const returnStmt = n === 0 ? 'return;' : n === 1 ? 'return out[0];' : 'return out as any;';
      const returnType =
        outArgs.length === 0
          ? 'void'
          : outArgs.length === 1
            ? `${mapType(outArgs[0].type)}`
            : `[${outArgs.map(a => mapType(a.type)).join(', ')}]`;

      output += `
  async ${toCamelCase(functionName)}(${parameters}): Promise<${returnType}> {
    ${inArgsVar}
    ${outTypesVar}
    ${wasmCall}
    ${outVar}
    ${returnStmt}
  }
`;
    } catch (err: any) {
      throw new Error(`Function ${functionName}: ${err.message}`);
    }
  }

  output += `}`;

  return output;
}

function generateSyncClass(functionDeclarations: FunctionDeclaration[]) {
  let output = `
export class BarretenbergApiSync {
  constructor(protected wasm: BarretenbergWasmMain) {}

`;

  for (const { functionName, inArgs, outArgs } of functionDeclarations) {
    try {
      const parameters = inArgs.map(({ name, type }) => `${toCamelCase(name)}: ${mapType(type)}`).join(', ');
      const inArgsVar = `const inArgs = [${inArgs
        .map(arg => toCamelCase(arg.name))
        .join(', ')}].map(serializeBufferable);`;
      const outTypesVar = `const outTypes: OutputType[] = [${outArgs
        .map(arg => mapDeserializer(arg.type))
        .join(', ')}];`;
      const wasmCall = `const result = this.wasm.callWasmExport('${functionName}', inArgs, outTypes.map(t=>t.SIZE_IN_BYTES));`;
      const outVar = `const out = result.map((r, i) => outTypes[i].fromBuffer(r));`;

      const n = outArgs.length;
      const returnStmt = n === 0 ? 'return;' : n === 1 ? 'return out[0];' : 'return out as any;';
      const returnType =
        outArgs.length === 0
          ? 'void'
          : outArgs.length === 1
            ? `${mapType(outArgs[0].type)}`
            : `[${outArgs.map(a => mapType(a.type)).join(', ')}]`;

      output += `
  ${toCamelCase(functionName)}(${parameters}): ${returnType} {
    ${inArgsVar}
    ${outTypesVar}
    ${wasmCall}
    ${outVar}
    ${returnStmt}
  }
`;
    } catch (err: any) {
      throw new Error(`Function ${functionName}: ${err.message}`);
    }
  }

  output += `}`;

  return output;
}
