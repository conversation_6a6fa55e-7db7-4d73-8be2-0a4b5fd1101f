import type { AztecAddress } from '@aztec/stdlib/aztec-address';
import { TxExecutionRequest } from '@aztec/stdlib/tx';
import type { AuthWitnessProvider, EntrypointInterface, FeeOptions, TxExecutionOptions } from './interfaces.js';
import { ExecutionPayload } from './payload.js';
/**
 * Implementation for an entrypoint interface that follows the default entrypoint signature
 * for an account, which accepts an AppPayload and a FeePayload as defined in noir-libs/aztec-noir/src/entrypoint module
 */
export declare class DefaultDappEntrypoint implements EntrypointInterface {
    private userAddress;
    private userAuthWitnessProvider;
    private dappEntrypointAddress;
    private chainId;
    private version;
    constructor(userAddress: AztecAddress, userAuthWitnessProvider: AuthWitnessProvider, dappEntrypointAddress: AztecAddress, chainId?: number, version?: number);
    createTxExecutionRequest(exec: ExecutionPayload, fee: FeeOptions, options: TxExecutionOptions): Promise<TxExecutionRequest>;
    private getEntrypointAbi;
}
//# sourceMappingURL=dapp_entrypoint.d.ts.map