import { TxExecutionRequest } from '@aztec/stdlib/tx';
import type { EntrypointInterface, FeeOptions, TxExecutionOptions } from './interfaces.js';
import type { ExecutionPayload } from './payload.js';
/**
 * Default implementation of the entrypoint interface. It calls a function on a contract directly
 */
export declare class DefaultEntrypoint implements EntrypointInterface {
    private chainId;
    private rollupVersion;
    constructor(chainId: number, rollupVersion: number);
    createTxExecutionRequest(exec: ExecutionPayload, fee: FeeOptions, options: TxExecutionOptions): Promise<TxExecutionRequest>;
}
//# sourceMappingURL=default_entrypoint.d.ts.map