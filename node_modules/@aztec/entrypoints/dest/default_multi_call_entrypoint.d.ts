import type { AztecAddress } from '@aztec/stdlib/aztec-address';
import { TxExecutionRequest } from '@aztec/stdlib/tx';
import type { EntrypointInterface, FeeOptions } from './interfaces.js';
import type { ExecutionPayload } from './payload.js';
/**
 * Implementation for an entrypoint interface that can execute multiple function calls in a single transaction
 */
export declare class DefaultMultiCallEntrypoint implements EntrypointInterface {
    private chainId;
    private version;
    private address;
    constructor(chainId: number, version: number, address?: AztecAddress);
    createTxExecutionRequest(exec: ExecutionPayload, fee: FeeOptions): Promise<TxExecutionRequest>;
    private getEntrypointAbi;
}
//# sourceMappingURL=default_multi_call_entrypoint.d.ts.map