import { Fr } from '@aztec/foundation/fields';
import type { <PERSON>ple } from '@aztec/foundation/serialize';
import { FunctionCall } from '@aztec/stdlib/abi';
import { HashedValues } from '@aztec/stdlib/tx';
declare const APP_MAX_CALLS = 4;
declare const FEE_MAX_CALLS = 2;
/** Encoded function call for an Aztec entrypoint */
export type EncodedFunctionCall = {
    /** Arguments hash for the call. */
    /** This should be the calldata hash `hash([function_selector, ...args])` if `is_public` is true. */
    args_hash: Fr;
    /** Selector of the function to call */
    function_selector: Fr;
    /** Address of the contract to call */
    target_address: Fr;
    /** Whether the function is public or private */
    is_public: boolean;
    /** Whether the function can alter state */
    is_static: boolean;
};
/** Type that represents function calls ready to be sent to a circuit for execution */
export type EncodedCalls = {
    /** Function calls in the expected format (Noir's convention) */
    encodedFunctionCalls: EncodedFunctionCall[];
    /** The hashed args for the call, ready to be injected in the execution cache */
    hashedArguments: HashedValues[];
};
/**
 * Entrypoints derive their arguments from the calls that they'll ultimate make.
 * This utility class helps in creating the payload for the entrypoint by taking into
 * account how the calls are encoded and hashed.
 * */
export declare abstract class EncodedCallsForEntrypoint implements EncodedCalls {
    /** Function calls in the expected format (Noir's convention) */
    encodedFunctionCalls: EncodedFunctionCall[];
    /** The hashed args for the call, ready to be injected in the execution cache */
    hashedArguments: HashedValues[];
    /** The index of the generator to use for hashing */
    generatorIndex: number;
    /** The nonce for the payload, used to emit a nullifier identifying the call */
    nonce: Fr;
    constructor(
    /** Function calls in the expected format (Noir's convention) */
    encodedFunctionCalls: EncodedFunctionCall[], 
    /** The hashed args for the call, ready to be injected in the execution cache */
    hashedArguments: HashedValues[], 
    /** The index of the generator to use for hashing */
    generatorIndex: number, 
    /** The nonce for the payload, used to emit a nullifier identifying the call */
    nonce: Fr);
    /**
     * The function calls to execute. This uses snake_case naming so that it is compatible with Noir encoding
     * @internal
     */
    get function_calls(): EncodedFunctionCall[];
    /**
     * Serializes the payload to an array of fields
     * @returns The fields of the payload
     */
    abstract toFields(): Fr[];
    /**
     * Hashes the payload
     * @returns The hash of the payload
     */
    hash(): Promise<Fr>;
    /** Serializes the function calls to an array of fields. */
    protected functionCallsToFields(): Fr[];
    /**
     * Encodes a set of function calls for a dapp entrypoint
     * @param functionCalls - The function calls to execute
     * @returns The encoded calls
     */
    static fromFunctionCalls(functionCalls: FunctionCall[]): Promise<EncodedAppEntrypointCalls>;
    /**
     * Encodes the functions for the app-portion of a transaction from a set of function calls and a nonce
     * @param functionCalls - The function calls to execute
     * @param nonce - The nonce for the payload, used to emit a nullifier identifying the call
     * @returns The encoded calls
     */
    static fromAppExecution(functionCalls: FunctionCall[] | Tuple<FunctionCall, typeof APP_MAX_CALLS>, nonce?: Fr): Promise<EncodedAppEntrypointCalls>;
    /**
     * Creates an encoded set of functions to pay the fee for a transaction
     * @param functionCalls - The calls generated by the payment method
     * @param isFeePayer - Whether the sender should be appointed as fee payer
     * @returns The encoded calls
     */
    static fromFeeCalls(functionCalls: FunctionCall[] | Tuple<FunctionCall, typeof FEE_MAX_CALLS>, isFeePayer: boolean): Promise<EncodedFeeEntrypointCalls>;
}
/** Encoded calls for app phase execution. */
export declare class EncodedAppEntrypointCalls extends EncodedCallsForEntrypoint {
    constructor(encodedFunctionCalls: EncodedFunctionCall[], hashedArguments: HashedValues[], generatorIndex: number, nonce: Fr);
    toFields(): Fr[];
}
/** Encoded calls for fee payment */
export declare class EncodedFeeEntrypointCalls extends EncodedCallsForEntrypoint {
    #private;
    constructor(encodedFunctionCalls: EncodedFunctionCall[], hashedArguments: HashedValues[], generatorIndex: number, nonce: Fr, isFeePayer: boolean);
    toFields(): Fr[];
    /** Whether the sender should be appointed as fee payer. */
    get is_fee_payer(): boolean;
}
/**
 * Computes a hash of a combined set of app and fee calls.
 * @param appCalls - A set of app calls.
 * @param feeCalls - A set of calls used to pay fees.
 * @returns A hash of a combined call set.
 */
export declare function computeCombinedPayloadHash(appPayload: EncodedAppEntrypointCalls, feePayload: EncodedFeeEntrypointCalls): Promise<Fr>;
/** Encodes FunctionCalls for execution, following Noir's convention */
export declare function encode(calls: FunctionCall[]): Promise<EncodedCalls>;
export {};
//# sourceMappingURL=encoding.d.ts.map