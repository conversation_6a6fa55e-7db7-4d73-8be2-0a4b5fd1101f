/**
 * The `@aztec/accounts/defaults` export provides the base class {@link DefaultAccountContract} for implementing account contracts that use the default entrypoint payload module.
 *
 * Read more in {@link https://docs.aztec.network/developers/tutorials/codealong/contract_tutorials/write_accounts_contract | Writing an account contract}.
 *
 * @packageDocumentation
 */
export * from './account_entrypoint.js';
export * from './dapp_entrypoint.js';
export * from './interfaces.js';
export * from './default_entrypoint.js';
export * from './encoding.js';
export * from './default_multi_call_entrypoint.js';
//# sourceMappingURL=index.d.ts.map