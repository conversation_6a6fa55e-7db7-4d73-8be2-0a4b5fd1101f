import { FunctionCall } from '@aztec/stdlib/abi';
import type { AuthWitness } from '@aztec/stdlib/auth-witness';
import { Capsule, HashedValues } from '@aztec/stdlib/tx';
/**
 * Represents data necessary to perform an action in the network successfully.
 * This class can be considered Aztec's "minimal execution unit".
 * */
export declare class ExecutionPayload {
    /** The function calls to be executed. */
    calls: FunctionCall[];
    /** Any transient auth witnesses needed for this execution */
    authWitnesses: AuthWitness[];
    /** Data passed through an oracle for this execution. */
    capsules: Capsule[];
    /** Extra hashed values to be injected in the execution cache */
    extraHashedArgs: HashedValues[];
    constructor(
    /** The function calls to be executed. */
    calls: FunctionCall[], 
    /** Any transient auth witnesses needed for this execution */
    authWitnesses: AuthWitness[], 
    /** Data passed through an oracle for this execution. */
    capsules: Capsule[], 
    /** Extra hashed values to be injected in the execution cache */
    extraHashedArgs?: HashedValues[]);
    static empty(): ExecutionPayload;
}
/**
 * Merges an array ExecutionPayloads combining their calls, authWitnesses, capsules and extraArgHashes.
 */
export declare function mergeExecutionPayloads(requests: ExecutionPayload[]): ExecutionPayload;
//# sourceMappingURL=payload.d.ts.map