import { GeneratorIndex } from '@aztec/constants';
import { padArrayEnd } from '@aztec/foundation/collection';
import { poseidon2HashWithSeparator } from '@aztec/foundation/crypto';
import { Fr } from '@aztec/foundation/fields';
import type { Tuple } from '@aztec/foundation/serialize';
import { FunctionCall, FunctionType } from '@aztec/stdlib/abi';
import { HashedValues } from '@aztec/stdlib/tx';

// These must match the values defined in:
// - noir-projects/aztec-nr/aztec/src/entrypoint/app.nr
const APP_MAX_CALLS = 4;
// - and noir-projects/aztec-nr/aztec/src/entrypoint/fee.nr
const FEE_MAX_CALLS = 2;

/** Encoded function call for an Aztec entrypoint */
export type EncodedFunctionCall = {
  /** Arguments hash for the call. */
  /** This should be the calldata hash `hash([function_selector, ...args])` if `is_public` is true. */
  args_hash: Fr;
  /** Selector of the function to call */
  function_selector: Fr;
  /** Address of the contract to call */
  target_address: Fr;
  /** Whether the function is public or private */
  is_public: boolean;
  /** Whether the function can alter state */
  is_static: boolean;
};

/** Type that represents function calls ready to be sent to a circuit for execution */
export type EncodedCalls = {
  /** Function calls in the expected format (Noir's convention) */
  encodedFunctionCalls: EncodedFunctionCall[];
  /** The hashed args for the call, ready to be injected in the execution cache */
  hashedArguments: HashedValues[];
};

/**
 * Entrypoints derive their arguments from the calls that they'll ultimate make.
 * This utility class helps in creating the payload for the entrypoint by taking into
 * account how the calls are encoded and hashed.
 * */
export abstract class EncodedCallsForEntrypoint implements EncodedCalls {
  constructor(
    /** Function calls in the expected format (Noir's convention) */
    public encodedFunctionCalls: EncodedFunctionCall[],
    /** The hashed args for the call, ready to be injected in the execution cache */
    public hashedArguments: HashedValues[],
    /** The index of the generator to use for hashing */
    public generatorIndex: number,
    /** The nonce for the payload, used to emit a nullifier identifying the call */
    public nonce: Fr,
  ) {}

  /* eslint-disable camelcase */
  /**
   * The function calls to execute. This uses snake_case naming so that it is compatible with Noir encoding
   * @internal
   */
  get function_calls() {
    return this.encodedFunctionCalls;
  }
  /* eslint-enable camelcase */

  /**
   * Serializes the payload to an array of fields
   * @returns The fields of the payload
   */
  abstract toFields(): Fr[];

  /**
   * Hashes the payload
   * @returns The hash of the payload
   */
  hash() {
    return poseidon2HashWithSeparator(this.toFields(), this.generatorIndex);
  }

  /** Serializes the function calls to an array of fields. */
  protected functionCallsToFields() {
    return this.encodedFunctionCalls.flatMap(call => [
      call.args_hash,
      call.function_selector,
      call.target_address,
      new Fr(call.is_public),
      new Fr(call.is_static),
    ]);
  }

  /**
   * Encodes a set of function calls for a dapp entrypoint
   * @param functionCalls - The function calls to execute
   * @returns The encoded calls
   */
  static async fromFunctionCalls(functionCalls: FunctionCall[]) {
    const encoded = await encode(functionCalls);
    return new EncodedAppEntrypointCalls(encoded.encodedFunctionCalls, encoded.hashedArguments, 0, Fr.random());
  }

  /**
   * Encodes the functions for the app-portion of a transaction from a set of function calls and a nonce
   * @param functionCalls - The function calls to execute
   * @param nonce - The nonce for the payload, used to emit a nullifier identifying the call
   * @returns The encoded calls
   */
  static async fromAppExecution(
    functionCalls: FunctionCall[] | Tuple<FunctionCall, typeof APP_MAX_CALLS>,
    nonce = Fr.random(),
  ) {
    if (functionCalls.length > APP_MAX_CALLS) {
      throw new Error(`Expected at most ${APP_MAX_CALLS} function calls, got ${functionCalls.length}`);
    }
    const paddedCalls = padArrayEnd(functionCalls, FunctionCall.empty(), APP_MAX_CALLS);
    const encoded = await encode(paddedCalls);
    return new EncodedAppEntrypointCalls(
      encoded.encodedFunctionCalls,
      encoded.hashedArguments,
      GeneratorIndex.SIGNATURE_PAYLOAD,
      nonce,
    );
  }

  /**
   * Creates an encoded set of functions to pay the fee for a transaction
   * @param functionCalls - The calls generated by the payment method
   * @param isFeePayer - Whether the sender should be appointed as fee payer
   * @returns The encoded calls
   */
  static async fromFeeCalls(
    functionCalls: FunctionCall[] | Tuple<FunctionCall, typeof FEE_MAX_CALLS>,
    isFeePayer: boolean,
  ) {
    const paddedCalls = padArrayEnd(functionCalls, FunctionCall.empty(), FEE_MAX_CALLS);
    const encoded = await encode(paddedCalls);
    return new EncodedFeeEntrypointCalls(
      encoded.encodedFunctionCalls,
      encoded.hashedArguments,
      GeneratorIndex.FEE_PAYLOAD,
      Fr.random(),
      isFeePayer,
    );
  }
}

/** Encoded calls for app phase execution. */
export class EncodedAppEntrypointCalls extends EncodedCallsForEntrypoint {
  constructor(
    encodedFunctionCalls: EncodedFunctionCall[],
    hashedArguments: HashedValues[],
    generatorIndex: number,
    nonce: Fr,
  ) {
    super(encodedFunctionCalls, hashedArguments, generatorIndex, nonce);
  }

  override toFields(): Fr[] {
    return [...this.functionCallsToFields(), this.nonce];
  }
}

/** Encoded calls for fee payment */
export class EncodedFeeEntrypointCalls extends EncodedCallsForEntrypoint {
  #isFeePayer: boolean;

  constructor(
    encodedFunctionCalls: EncodedFunctionCall[],
    hashedArguments: HashedValues[],
    generatorIndex: number,
    nonce: Fr,
    isFeePayer: boolean,
  ) {
    super(encodedFunctionCalls, hashedArguments, generatorIndex, nonce);
    this.#isFeePayer = isFeePayer;
  }

  override toFields(): Fr[] {
    return [...this.functionCallsToFields(), this.nonce, new Fr(this.#isFeePayer)];
  }

  /* eslint-disable camelcase */
  /** Whether the sender should be appointed as fee payer. */
  get is_fee_payer() {
    return this.#isFeePayer;
  }
  /* eslint-enable camelcase */
}

/**
 * Computes a hash of a combined set of app and fee calls.
 * @param appCalls - A set of app calls.
 * @param feeCalls - A set of calls used to pay fees.
 * @returns A hash of a combined call set.
 */
export async function computeCombinedPayloadHash(
  appPayload: EncodedAppEntrypointCalls,
  feePayload: EncodedFeeEntrypointCalls,
): Promise<Fr> {
  return poseidon2HashWithSeparator(
    [await appPayload.hash(), await feePayload.hash()],
    GeneratorIndex.COMBINED_PAYLOAD,
  );
}

/** Encodes FunctionCalls for execution, following Noir's convention */
export async function encode(calls: FunctionCall[]): Promise<EncodedCalls> {
  const hashedArguments: HashedValues[] = [];
  for (const call of calls) {
    const hashed =
      call.type === FunctionType.PUBLIC
        ? await HashedValues.fromCalldata([call.selector.toField(), ...call.args])
        : await HashedValues.fromArgs(call.args);
    hashedArguments.push(hashed);
  }

  /* eslint-disable camelcase */
  const encodedFunctionCalls: EncodedFunctionCall[] = calls.map((call, index) => ({
    args_hash: hashedArguments[index].hash,
    function_selector: call.selector.toField(),
    target_address: call.to.toField(),
    is_public: call.type == FunctionType.PUBLIC,
    is_static: call.isStatic,
  }));

  return {
    encodedFunctionCalls,
    hashedArguments,
  };
}
