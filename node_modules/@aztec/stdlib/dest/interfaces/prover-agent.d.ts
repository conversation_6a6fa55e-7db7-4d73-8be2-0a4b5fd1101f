import { z } from 'zod';
import type { ApiSchemaFor } from '../schemas/index.js';
export declare const ProverAgentStatusSchema: z.ZodDiscriminatedUnion<"status", [z.ZodObject<{
    status: z.<PERSON>od<PERSON>iteral<"stopped">;
}, "strip", z.Zod<PERSON>ype<PERSON>ny, {
    status: "stopped";
}, {
    status: "stopped";
}>, z.ZodObject<{
    status: z.ZodLiteral<"running">;
}, "strip", z.ZodTypeAny, {
    status: "running";
}, {
    status: "running";
}>, z.ZodObject<{
    status: z.ZodLiteral<"proving">;
    jobId: z.ZodString;
    proofType: z.ZodNumber;
    startedAtISO: z.ZodString;
}, "strip", z.ZodTypeAny, {
    status: "proving";
    jobId: string;
    proofType: number;
    startedAtISO: string;
}, {
    status: "proving";
    jobId: string;
    proofType: number;
    startedAtISO: string;
}>]>;
export type ProverAgentStatus = z.infer<typeof ProverAgentStatusSchema>;
export interface ProverAgentApi {
    getStatus(): Promise<unknown>;
}
export declare const ProverAgentApiSchema: ApiSchemaFor<ProverAgentApi>;
//# sourceMappingURL=prover-agent.d.ts.map