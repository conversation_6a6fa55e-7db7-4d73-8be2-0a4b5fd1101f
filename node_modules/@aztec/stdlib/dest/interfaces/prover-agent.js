import { z } from 'zod';
export const ProverAgentStatusSchema = z.discriminatedUnion('status', [
    z.object({
        status: z.literal('stopped')
    }),
    z.object({
        status: z.literal('running')
    }),
    z.object({
        status: z.literal('proving'),
        jobId: z.string(),
        proofType: z.number(),
        startedAtISO: z.string()
    })
]);
export const ProverAgentApiSchema = {
    getStatus: z.function().args().returns(ProverAgentStatusSchema)
};
