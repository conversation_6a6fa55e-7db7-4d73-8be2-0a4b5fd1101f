{"version": 3, "file": "prover-broker.d.ts", "sourceRoot": "", "sources": ["../../src/interfaces/prover-broker.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,kBAAkB,EAAE,MAAM,mCAAmC,CAAC;AAC5E,OAAO,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE,YAAY,EAAE,gBAAgB,EAAE,MAAM,kBAAkB,CAAC;AAE7F;;GAEG;AACH,MAAM,WAAW,kBAAkB;IACjC;;;OAGG;IACH,iBAAiB,CAAC,GAAG,EAAE,UAAU,GAAG,OAAO,CAAC,gBAAgB,CAAC,CAAC;IAE9D;;;OAGG;IACH,gBAAgB,CAAC,EAAE,EAAE,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAElD;;;OAGG;IACH,mBAAmB,CAAC,EAAE,EAAE,YAAY,GAAG,OAAO,CAAC,gBAAgB,CAAC,CAAC;IAEjE;;;;OAIG;IACH,gBAAgB,CAAC,GAAG,EAAE,YAAY,EAAE,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,CAAC;CAChE;AAED,MAAM,MAAM,gBAAgB,GAAG;IAC7B,SAAS,EAAE,kBAAkB,EAAE,CAAC;CACjC,CAAC;AAEF,MAAM,MAAM,qBAAqB,GAAG;IAClC,GAAG,EAAE,UAAU,CAAC;IAChB,IAAI,EAAE,MAAM,CAAC;CACd,CAAC;AAEF;;GAEG;AACH,MAAM,WAAW,kBAAkB;IACjC;;;OAGG;IACH,aAAa,CAAC,MAAM,CAAC,EAAE,gBAAgB,GAAG,OAAO,CAAC,qBAAqB,GAAG,SAAS,CAAC,CAAC;IAErF;;;;OAIG;IACH,uBAAuB,CACrB,EAAE,EAAE,YAAY,EAChB,MAAM,EAAE,QAAQ,EAChB,MAAM,CAAC,EAAE,gBAAgB,GACxB,OAAO,CAAC,qBAAqB,GAAG,SAAS,CAAC,CAAC;IAE9C;;;;;OAKG;IACH,qBAAqB,CACnB,EAAE,EAAE,YAAY,EAChB,GAAG,EAAE,MAAM,EACX,KAAK,CAAC,EAAE,OAAO,EACf,MAAM,CAAC,EAAE,gBAAgB,GACxB,OAAO,CAAC,qBAAqB,GAAG,SAAS,CAAC,CAAC;IAE9C;;;;;OAKG;IACH,wBAAwB,CACtB,EAAE,EAAE,YAAY,EAChB,SAAS,EAAE,MAAM,EACjB,MAAM,CAAC,EAAE,gBAAgB,GACxB,OAAO,CAAC,qBAAqB,GAAG,SAAS,CAAC,CAAC;CAC/C;AAED,MAAM,WAAW,gBAAiB,SAAQ,kBAAkB,EAAE,kBAAkB;CAAG"}