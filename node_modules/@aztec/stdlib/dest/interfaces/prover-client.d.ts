import { type ConfigMappingsType } from '@aztec/foundation/config';
import { Fr } from '@aztec/foundation/fields';
import { z } from 'zod';
import { type ZodFor } from '../schemas/index.js';
import type { TxHash } from '../tx/tx_hash.js';
import type { EpochProver } from './epoch-prover.js';
import type { ProvingJobConsumer } from './prover-broker.js';
export type ActualProverConfig = {
    /** Whether to construct real proofs */
    realProofs: boolean;
    /** The type of artificial delay to introduce */
    proverTestDelayType: 'fixed' | 'realistic';
    /** If using fixed delay, the time each operation takes. */
    proverTestDelayMs: number;
    /** If using realistic delays, what percentage of realistic times to apply. */
    proverTestDelayFactor: number;
};
/**
 * The prover configuration.
 */
export type ProverConfig = ActualProverConfig & {
    /** The URL to the Aztec node to take proving jobs from */
    nodeUrl?: string;
    /** Identifier of the prover */
    proverId?: Fr;
    /** Number of proving agents to start within the prover. */
    proverAgentCount: number;
    /** Store for failed proof inputs. */
    failedProofStore?: string;
};
export declare const ProverConfigSchema: z.ZodObject<{
    nodeUrl: z.ZodOptional<z.ZodString>;
    realProofs: z.ZodBoolean;
    proverId: z.ZodOptional<ZodFor<Fr>>;
    proverTestDelayType: z.ZodEnum<["fixed", "realistic"]>;
    proverTestDelayMs: z.ZodNumber;
    proverTestDelayFactor: z.ZodNumber;
    proverAgentCount: z.ZodNumber;
}, "strip", z.ZodTypeAny, {
    realProofs: boolean;
    proverTestDelayType: "fixed" | "realistic";
    proverTestDelayMs: number;
    proverTestDelayFactor: number;
    proverAgentCount: number;
    proverId?: Fr | undefined;
    nodeUrl?: string | undefined;
}, {
    realProofs: boolean;
    proverTestDelayType: "fixed" | "realistic";
    proverTestDelayMs: number;
    proverTestDelayFactor: number;
    proverAgentCount: number;
    proverId?: any;
    nodeUrl?: string | undefined;
}>;
export declare const proverConfigMappings: ConfigMappingsType<ProverConfig>;
/**
 * The interface to the prover client.
 * Provides the ability to generate proofs and build rollups.
 */
export interface EpochProverManager {
    createEpochProver(): EpochProver;
    start(): Promise<void>;
    stop(): Promise<void>;
    getProvingJobSource(): ProvingJobConsumer;
    getProverId(): Fr;
    updateProverConfig(config: Partial<ProverConfig>): Promise<void>;
}
export declare class BlockProofError extends Error {
    #private;
    readonly txHashes: TxHash[];
    name: string;
    constructor(message: string, txHashes: TxHash[]);
    static isBlockProofError(err: any): err is BlockProofError;
}
//# sourceMappingURL=prover-client.d.ts.map