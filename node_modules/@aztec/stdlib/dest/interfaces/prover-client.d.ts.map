{"version": 3, "file": "prover-client.d.ts", "sourceRoot": "", "sources": ["../../src/interfaces/prover-client.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,kBAAkB,EAA2C,MAAM,0BAA0B,CAAC;AAC5G,OAAO,EAAE,EAAE,EAAE,MAAM,0BAA0B,CAAC;AAE9C,OAAO,EAAE,CAAC,EAAE,MAAM,KAAK,CAAC;AAExB,OAAO,EAAE,KAAK,MAAM,EAAW,MAAM,qBAAqB,CAAC;AAC3D,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,kBAAkB,CAAC;AAC/C,OAAO,KAAK,EAAE,WAAW,EAAE,MAAM,mBAAmB,CAAC;AACrD,OAAO,KAAK,EAAE,kBAAkB,EAAE,MAAM,oBAAoB,CAAC;AAE7D,MAAM,MAAM,kBAAkB,GAAG;IAC/B,uCAAuC;IACvC,UAAU,EAAE,OAAO,CAAC;IACpB,gDAAgD;IAChD,mBAAmB,EAAE,OAAO,GAAG,WAAW,CAAC;IAC3C,2DAA2D;IAC3D,iBAAiB,EAAE,MAAM,CAAC;IAC1B,8EAA8E;IAC9E,qBAAqB,EAAE,MAAM,CAAC;CAC/B,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,YAAY,GAAG,kBAAkB,GAAG;IAC9C,0DAA0D;IAC1D,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,+BAA+B;IAC/B,QAAQ,CAAC,EAAE,EAAE,CAAC;IACd,2DAA2D;IAC3D,gBAAgB,EAAE,MAAM,CAAC;IACzB,qCAAqC;IACrC,gBAAgB,CAAC,EAAE,MAAM,CAAC;CAC3B,CAAC;AAEF,eAAO,MAAM,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;EAQE,CAAC;AAElC,eAAO,MAAM,oBAAoB,EAAE,kBAAkB,CAAC,YAAY,CAuCjE,CAAC;AASF;;;GAGG;AACH,MAAM,WAAW,kBAAkB;IACjC,iBAAiB,IAAI,WAAW,CAAC;IAEjC,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;IAEvB,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;IAEtB,mBAAmB,IAAI,kBAAkB,CAAC;IAE1C,WAAW,IAAI,EAAE,CAAC;IAElB,kBAAkB,CAAC,MAAM,EAAE,OAAO,CAAC,YAAY,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;CAClE;AAED,qBAAa,eAAgB,SAAQ,KAAK;;aAMtB,QAAQ,EAAE,MAAM,EAAE;IAJ3B,IAAI,SAAyB;gBAGpC,OAAO,EAAE,MAAM,EACC,QAAQ,EAAE,MAAM,EAAE;IAKpC,MAAM,CAAC,iBAAiB,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,IAAI,eAAe;CAG3D"}