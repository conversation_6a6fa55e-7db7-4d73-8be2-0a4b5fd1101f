import { type L2Tips } from '../block/l2_block_source.js';
import { type ApiSchemaFor } from '../schemas/index.js';
import { type WorldStateSyncStatus } from './world_state.js';
declare const EpochProvingJobState: readonly ["initialized", "processing", "awaiting-prover", "publishing-proof", "completed", "failed", "stopped", "timed-out", "reorg"];
export type EpochProvingJobState = (typeof EpochProvingJobState)[number];
export declare const EpochProvingJobTerminalState: EpochProvingJobState[];
export type EpochProvingJobTerminalState = (typeof EpochProvingJobTerminalState)[number];
/** JSON RPC public interface to a prover node. */
export interface ProverNodeApi {
    getJobs(): Promise<{
        uuid: string;
        status: EpochProvingJobState;
        epochNumber: number;
    }[]>;
    startProof(epochNumber: number): Promise<void>;
    getL2Tips(): Promise<L2Tips>;
    getWorldStateSyncStatus(): Promise<WorldStateSyncStatus>;
}
/** Schemas for prover node API functions. */
export declare const ProverNodeApiSchema: ApiSchemaFor<ProverNodeApi>;
export {};
//# sourceMappingURL=prover-node.d.ts.map