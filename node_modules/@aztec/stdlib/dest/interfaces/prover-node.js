import { z } from 'zod';
import { L2TipsSchema } from '../block/l2_block_source.js';
import { schemas } from '../schemas/index.js';
import { WorldStateSyncStatusSchema } from './world_state.js';
const EpochProvingJobState = [
    'initialized',
    'processing',
    'awaiting-prover',
    'publishing-proof',
    'completed',
    'failed',
    'stopped',
    'timed-out',
    'reorg'
];
export const EpochProvingJobTerminalState = [
    'completed',
    'failed',
    'stopped',
    'timed-out',
    'reorg'
];
/** Schemas for prover node API functions. */ export const ProverNodeApiSchema = {
    getJobs: z.function().args().returns(z.array(z.object({
        uuid: z.string(),
        status: z.enum(EpochProvingJobState),
        epochNumber: z.number()
    }))),
    startProof: z.function().args(schemas.Integer).returns(z.void()),
    getL2Tips: z.function().args().returns(L2TipsSchema),
    getWorldStateSyncStatus: z.function().args().returns(WorldStateSyncStatusSchema)
};
