import { z } from 'zod';
import { ProvingJob, ProvingJobId, ProvingJobR<PERSON>ult } from './proving-job.js';
export const ProvingJobSourceSchema = {
    getProvingJob: z.function().args().returns(ProvingJob.optional()),
    heartbeat: z.function().args(ProvingJobId).returns(z.void()),
    resolveProvingJob: z.function().args(ProvingJobId, ProvingJobResult).returns(z.void()),
    rejectProvingJob: z.function().args(ProvingJobId, z.string()).returns(z.void())
};
