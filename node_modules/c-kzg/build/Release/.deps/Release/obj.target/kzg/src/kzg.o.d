cmd_Release/obj.target/kzg/src/kzg.o := c++ -o Release/obj.target/kzg/src/kzg.o ../src/kzg.cxx '-DNODE_GYP_MODULE_NAME=kzg' '-DUSING_UV_SHARED=1' '-DUSING_V8_SHARED=1' '-DV8_DEPRECATION_WARNINGS=1' '-DV8_DEPRECATION_WARNINGS' '-DV8_IMMINENT_DEPRECATION_WARNINGS' '-D_GLIBCXX_USE_CXX11_ABI=1' '-D_DARWIN_USE_64_BIT_INODE=1' '-D_LARGEFILE_SOURCE' '-D_FILE_OFFSET_BITS=64' '-DOPENSSL_NO_PINSHARED' '-DOPENSSL_THREADS' '-D__BLST_PORTABLE__' '-DNAPI_CPP_EXCEPTIONS' '-DBUILDING_NODE_EXTENSION' -I/Users/<USER>/Library/Caches/node-gyp/18.20.7/include/node -I/Users/<USER>/Library/Caches/node-gyp/18.20.7/src -I/Users/<USER>/Library/Caches/node-gyp/18.20.7/deps/openssl/config -I/Users/<USER>/Library/Caches/node-gyp/18.20.7/deps/openssl/openssl/include -I/Users/<USER>/Library/Caches/node-gyp/18.20.7/deps/uv/include -I/Users/<USER>/Library/Caches/node-gyp/18.20.7/deps/zlib -I/Users/<USER>/Library/Caches/node-gyp/18.20.7/deps/v8/include -I/Users/<USER>/Developer/mess-age/node_modules/c-kzg/deps/blst/bindings -I/Users/<USER>/Developer/mess-age/node_modules/c-kzg/deps/c-kzg -I/Users/<USER>/Developer/mess-age/node_modules/node-addon-api  -O3 -gdwarf-2 -mmacosx-version-min=13.0 -arch arm64 -Wall -Wendif-labels -W -Wno-unused-parameter -std=gnu++17 -stdlib=libc++ -fno-rtti -fno-strict-aliasing -MMD -MF ./Release/.deps/Release/obj.target/kzg/src/kzg.o.d.raw   -c
Release/obj.target/kzg/src/kzg.o: ../src/kzg.cxx \
  /Users/<USER>/Developer/mess-age/node_modules/c-kzg/deps/blst/bindings/blst.h \
  /Users/<USER>/Developer/mess-age/node_modules/c-kzg/deps/blst/bindings/blst_aux.h \
  /Users/<USER>/Developer/mess-age/node_modules/c-kzg/deps/c-kzg/c_kzg_4844.h \
  /Users/<USER>/Developer/mess-age/node_modules/node-addon-api/napi.h \
  /Users/<USER>/Library/Caches/node-gyp/18.20.7/include/node/node_api.h \
  /Users/<USER>/Library/Caches/node-gyp/18.20.7/include/node/js_native_api.h \
  /Users/<USER>/Library/Caches/node-gyp/18.20.7/include/node/js_native_api_types.h \
  /Users/<USER>/Library/Caches/node-gyp/18.20.7/include/node/node_api_types.h \
  /Users/<USER>/Developer/mess-age/node_modules/node-addon-api/napi-inl.h \
  /Users/<USER>/Developer/mess-age/node_modules/node-addon-api/napi-inl.deprecated.h
../src/kzg.cxx:
/Users/<USER>/Developer/mess-age/node_modules/c-kzg/deps/blst/bindings/blst.h:
/Users/<USER>/Developer/mess-age/node_modules/c-kzg/deps/blst/bindings/blst_aux.h:
/Users/<USER>/Developer/mess-age/node_modules/c-kzg/deps/c-kzg/c_kzg_4844.h:
/Users/<USER>/Developer/mess-age/node_modules/node-addon-api/napi.h:
/Users/<USER>/Library/Caches/node-gyp/18.20.7/include/node/node_api.h:
/Users/<USER>/Library/Caches/node-gyp/18.20.7/include/node/js_native_api.h:
/Users/<USER>/Library/Caches/node-gyp/18.20.7/include/node/js_native_api_types.h:
/Users/<USER>/Library/Caches/node-gyp/18.20.7/include/node/node_api_types.h:
/Users/<USER>/Developer/mess-age/node_modules/node-addon-api/napi-inl.h:
/Users/<USER>/Developer/mess-age/node_modules/node-addon-api/napi-inl.deprecated.h:
