{"version": 3, "file": "getUserOperationHash.js", "sourceRoot": "", "sources": ["../../../../account-abstraction/utils/userOperation/getUserOperationHash.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,mBAAmB,EAAE,MAAM,2CAA2C,CAAA;AAC/E,OAAO,EAAE,MAAM,EAAE,MAAM,+BAA+B,CAAA;AACtD,OAAO,EAAE,GAAG,EAAE,MAAM,4BAA4B,CAAA;AAChD,OAAO,EAAE,WAAW,EAAE,MAAM,kCAAkC,CAAA;AAC9D,OAAO,EAAE,SAAS,EAAE,MAAM,kCAAkC,CAAA;AAe5D,MAAM,UAAU,oBAAoB,CAGlC,UAA6D;IAE7D,MAAM,EAAE,OAAO,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,GAAG,UAAU,CAAA;IACpE,MAAM,aAAa,GAAG,UAAU,CAAC,aAA8B,CAAA;IAC/D,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,QAAQ,EACR,YAAY,EACZ,oBAAoB,EACpB,KAAK,EACL,gBAAgB,EAChB,kBAAkB,EAClB,MAAM,EACN,oBAAoB,GACrB,GAAG,aAAa,CAAA;IAEjB,MAAM,YAAY,GAAG,CAAC,GAAG,EAAE;QACzB,IAAI,iBAAiB,KAAK,KAAK,EAAE,CAAC;YAChC,OAAO,mBAAmB,CACxB;gBACE,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;aACpB,EACD;gBACE,MAAM;gBACN,KAAK;gBACL,SAAS,CAAC,QAAQ,IAAI,IAAI,CAAC;gBAC3B,SAAS,CAAC,QAAQ,IAAI,IAAI,CAAC;gBAC3B,YAAY;gBACZ,oBAAoB;gBACpB,kBAAkB;gBAClB,YAAY;gBACZ,oBAAoB;gBACpB,SAAS,CAAC,gBAAgB,IAAI,IAAI,CAAC;aACpC,CACF,CAAA;QACH,CAAC;QAED,IAAI,iBAAiB,KAAK,KAAK,EAAE,CAAC;YAChC,MAAM,gBAAgB,GAAG,MAAM,CAAC;gBAC9B,GAAG,CAAC,WAAW,CAAC,aAAa,CAAC,oBAAoB,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;gBAClE,GAAG,CAAC,WAAW,CAAC,aAAa,CAAC,YAAY,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;aAC3D,CAAC,CAAA;YACF,MAAM,eAAe,GAAG,SAAS,CAAC,QAAQ,CAAC,CAAA;YAC3C,MAAM,OAAO,GAAG,MAAM,CAAC;gBACrB,GAAG,CAAC,WAAW,CAAC,aAAa,CAAC,oBAAoB,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;gBAClE,GAAG,CAAC,WAAW,CAAC,aAAa,CAAC,YAAY,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;aAC3D,CAAC,CAAA;YACF,MAAM,eAAe,GAAG,SAAS,CAC/B,aAAa,CAAC,OAAO,IAAI,aAAa,CAAC,WAAW;gBAChD,CAAC,CAAC,MAAM,CAAC,CAAC,aAAa,CAAC,OAAO,EAAE,aAAa,CAAC,WAAW,CAAC,CAAC;gBAC5D,CAAC,CAAC,IAAI,CACT,CAAA;YACD,MAAM,uBAAuB,GAAG,SAAS,CACvC,aAAa,CAAC,SAAS;gBACrB,CAAC,CAAC,MAAM,CAAC;oBACL,aAAa,CAAC,SAAS;oBACvB,GAAG,CACD,WAAW,CAAC,aAAa,CAAC,6BAA6B,IAAI,CAAC,CAAC,EAC7D;wBACE,IAAI,EAAE,EAAE;qBACT,CACF;oBACD,GAAG,CAAC,WAAW,CAAC,aAAa,CAAC,uBAAuB,IAAI,CAAC,CAAC,EAAE;wBAC3D,IAAI,EAAE,EAAE;qBACT,CAAC;oBACF,aAAa,CAAC,aAAa,IAAI,IAAI;iBACpC,CAAC;gBACJ,CAAC,CAAC,IAAI,CACT,CAAA;YAED,OAAO,mBAAmB,CACxB;gBACE,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;aACpB,EACD;gBACE,MAAM;gBACN,KAAK;gBACL,eAAe;gBACf,eAAe;gBACf,gBAAgB;gBAChB,kBAAkB;gBAClB,OAAO;gBACP,uBAAuB;aACxB,CACF,CAAA;QACH,CAAC;QAED,MAAM,IAAI,KAAK,CAAC,sBAAsB,iBAAiB,kBAAkB,CAAC,CAAA;IAC5E,CAAC,CAAC,EAAE,CAAA;IAEJ,OAAO,SAAS,CACd,mBAAmB,CACjB,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,EAC/D,CAAC,SAAS,CAAC,YAAY,CAAC,EAAE,iBAAiB,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAC9D,CACF,CAAA;AACH,CAAC"}