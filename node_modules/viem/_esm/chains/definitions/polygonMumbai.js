import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js';
export const polygonMumbai = /*#__PURE__*/ define<PERSON>hain({
    id: 80_001,
    name: 'Polygon Mumbai',
    nativeCurrency: { name: '<PERSON><PERSON><PERSON>', symbol: '<PERSON><PERSON><PERSON>', decimals: 18 },
    rpcUrls: {
        default: {
            http: ['https://rpc.ankr.com/polygon_mumbai'],
        },
    },
    blockExplorers: {
        default: {
            name: 'PolygonScan',
            url: 'https://mumbai.polygonscan.com',
            apiUrl: 'https://api-testnet.polygonscan.com/api',
        },
    },
    contracts: {
        multicall3: {
            address: '0xca11bde05977b3631167028862be2a173976ca11',
            blockCreated: 25770160,
        },
    },
    testnet: true,
});
//# sourceMappingURL=polygonMumbai.js.map