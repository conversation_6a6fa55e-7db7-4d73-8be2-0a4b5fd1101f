export declare const donatuz: {
    blockExplorers: {
        readonly default: {
            readonly name: "Donatuz Explorer";
            readonly url: "https://explorer.donatuz.com";
        };
    };
    contracts: {
        readonly multicall3: {
            readonly address: "******************************************";
            readonly blockCreated: 0;
        };
    };
    id: 42026;
    name: "Donatuz";
    nativeCurrency: {
        readonly decimals: 18;
        readonly name: "<PERSON><PERSON>";
        readonly symbol: "ETH";
    };
    rpcUrls: {
        readonly default: {
            readonly http: readonly ["https://rpc.donatuz.com"];
        };
    };
    sourceId?: number | undefined;
    testnet?: boolean | undefined;
    custom?: Record<string, unknown> | undefined;
    fees?: import("../../index.js").ChainFees<undefined> | undefined;
    formatters?: undefined;
    serializers?: import("../../index.js").ChainSerializers<undefined, import("../../index.js").TransactionSerializable> | undefined;
};
//# sourceMappingURL=donatuz.d.ts.map