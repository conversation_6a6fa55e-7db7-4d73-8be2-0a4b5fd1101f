export declare const ethernity: {
    blockExplorers: {
        readonly default: {
            readonly name: "Ethernity Explorer";
            readonly url: "https://ernscan.io";
        };
    };
    contracts: {
        readonly multicall3: {
            readonly address: "******************************************";
            readonly blockCreated: 0;
        };
    };
    id: 183;
    name: "Ethernity";
    nativeCurrency: {
        readonly decimals: 18;
        readonly name: "<PERSON>ther";
        readonly symbol: "ETH";
    };
    rpcUrls: {
        readonly default: {
            readonly http: readonly ["https://mainnet.ethernitychain.io"];
        };
    };
    sourceId?: number | undefined;
    testnet: false;
    custom?: Record<string, unknown> | undefined;
    fees?: import("../../index.js").ChainFees<undefined> | undefined;
    formatters?: undefined;
    serializers?: import("../../index.js").ChainSerializers<undefined, import("../../index.js").TransactionSerializable> | undefined;
};
//# sourceMappingURL=ethernity.d.ts.map