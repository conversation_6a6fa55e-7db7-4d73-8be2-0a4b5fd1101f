export declare const juneo: {
    blockExplorers: {
        readonly default: {
            readonly name: "<PERSON><PERSON>";
            readonly url: "https://juneoscan.io/chain/2";
            readonly apiUrl: "https://juneoscan.io/chain/2/api";
        };
    };
    contracts?: import("../index.js").Prettify<{
        [key: string]: import("../../index.js").ChainContract | {
            [sourceId: number]: import("../../index.js").ChainContract | undefined;
        } | undefined;
    } & {
        ensRegistry?: import("../../index.js").ChainContract | undefined;
        ensUniversalResolver?: import("../../index.js").ChainContract | undefined;
        multicall3?: import("../../index.js").ChainContract | undefined;
        universalSignatureVerifier?: import("../../index.js").ChainContract | undefined;
    }> | undefined;
    id: 45003;
    name: "JUNE-Chain";
    nativeCurrency: {
        readonly decimals: 18;
        readonly name: "JUNE-Chain";
        readonly symbol: "JUNE";
    };
    rpcUrls: {
        readonly default: {
            readonly http: readonly ["https://rpc.juneo-mainnet.network/ext/bc/JUNE/rpc"];
        };
    };
    sourceId?: number | undefined;
    testnet?: boolean | undefined;
    custom?: Record<string, unknown> | undefined;
    fees?: import("../../index.js").ChainFees<undefined> | undefined;
    formatters?: undefined;
    serializers?: import("../../index.js").ChainSerializers<undefined, import("../../index.js").TransactionSerializable> | undefined;
};
//# sourceMappingURL=juneo.d.ts.map