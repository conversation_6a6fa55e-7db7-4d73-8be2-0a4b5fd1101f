export declare const morph<PERSON><PERSON>sky: {
    blockExplorers: {
        readonly default: {
            readonly name: "Morph Holesky Explorer";
            readonly url: "https://explorer-holesky.morphl2.io";
            readonly apiUrl: "https://explorer-api-holesky.morphl2.io/api?";
        };
    };
    contracts?: import("../index.js").Prettify<{
        [key: string]: import("../../index.js").ChainContract | {
            [sourceId: number]: import("../../index.js").ChainContract | undefined;
        } | undefined;
    } & {
        ensRegistry?: import("../../index.js").ChainContract | undefined;
        ensUniversalResolver?: import("../../index.js").ChainContract | undefined;
        multicall3?: import("../../index.js").ChainContract | undefined;
        universalSignatureVerifier?: import("../../index.js").ChainContract | undefined;
    }> | undefined;
    id: 2810;
    name: "<PERSON><PERSON><PERSON>";
    nativeCurrency: {
        readonly name: "<PERSON><PERSON>";
        readonly symbol: "ETH";
        readonly decimals: 18;
    };
    rpcUrls: {
        readonly default: {
            readonly http: readonly ["https://rpc-quicknode-holesky.morphl2.io"];
            readonly webSocket: readonly ["wss://rpc-quicknode-holesky.morphl2.io"];
        };
    };
    sourceId?: number | undefined;
    testnet: true;
    custom?: Record<string, unknown> | undefined;
    fees?: import("../../index.js").ChainFees<undefined> | undefined;
    formatters?: undefined;
    serializers?: import("../../index.js").ChainSerializers<undefined, import("../../index.js").TransactionSerializable> | undefined;
};
//# sourceMappingURL=morphHolesky.d.ts.map