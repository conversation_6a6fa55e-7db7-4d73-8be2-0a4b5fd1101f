export declare const storyAeneid: {
    blockExplorers: {
        readonly default: {
            readonly name: "Story Aeneid Explorer";
            readonly url: "https://aeneid.storyscan.xyz";
        };
    };
    contracts: {
        readonly multicall3: {
            readonly address: "0xca11bde05977b3631167028862be2a173976ca11";
            readonly blockCreated: 1792;
        };
    };
    id: 1315;
    name: "Story Aeneid";
    nativeCurrency: {
        readonly decimals: 18;
        readonly name: "IP";
        readonly symbol: "IP";
    };
    rpcUrls: {
        readonly default: {
            readonly http: readonly ["https://aeneid.storyrpc.io"];
        };
    };
    sourceId?: number | undefined;
    testnet: true;
    custom?: Record<string, unknown> | undefined;
    fees?: import("../../index.js").ChainFees<undefined> | undefined;
    formatters?: undefined;
    serializers?: import("../../index.js").ChainSerializers<undefined, import("../../index.js").TransactionSerializable> | undefined;
    readonly network: "story-aeneid";
};
//# sourceMappingURL=storyAeneid.d.ts.map