{"version": 3, "file": "eip5792.d.ts", "sourceRoot": "", "sources": ["../../../../experimental/eip5792/decorators/eip5792.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,kCAAkC,CAAA;AAC9D,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,gDAAgD,CAAA;AAC/E,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,2BAA2B,CAAA;AACxD,OAAO,KAAK,EAAE,KAAK,EAAE,MAAM,yBAAyB,CAAA;AACpD,OAAO,EACL,KAAK,wBAAwB,EAC7B,KAAK,wBAAwB,EAE9B,MAAM,8BAA8B,CAAA;AACrC,OAAO,EACL,KAAK,yBAAyB,EAC9B,KAAK,yBAAyB,EAE/B,MAAM,+BAA+B,CAAA;AACtC,OAAO,EACL,KAAK,mBAAmB,EACxB,KAAK,mBAAmB,EAEzB,MAAM,yBAAyB,CAAA;AAChC,OAAO,EACL,KAAK,yBAAyB,EAC9B,KAAK,yBAAyB,EAE/B,MAAM,+BAA+B,CAAA;AACtC,OAAO,EACL,KAAK,wBAAwB,EAC7B,KAAK,wBAAwB,EAE9B,MAAM,8BAA8B,CAAA;AAErC,MAAM,MAAM,cAAc,CACxB,KAAK,SAAS,KAAK,GAAG,SAAS,GAAG,KAAK,GAAG,SAAS,EACnD,OAAO,SAAS,OAAO,GAAG,SAAS,GAAG,OAAO,GAAG,SAAS,IACvD;IACF;;;;;;;;;;;;;;;;;;;;OAoBG;IACH,cAAc,EAAE,CACd,UAAU,EAAE,wBAAwB,KACjC,OAAO,CAAC,wBAAwB,CAAC,CAAA;IACtC;;;;;;;;;;;;;;;;;;;;;;OAsBG;IACH,eAAe,EAAE,CACf,UAAU,CAAC,EAAE,yBAAyB,KACnC,OAAO,CAAC,yBAAyB,CAAC,CAAA;IACvC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAgCG;IACH,SAAS,EAAE,CAAC,aAAa,SAAS,KAAK,GAAG,SAAS,GAAG,SAAS,EAC7D,UAAU,EAAE,mBAAmB,CAAC,KAAK,EAAE,OAAO,EAAE,aAAa,CAAC,KAC3D,OAAO,CAAC,mBAAmB,CAAC,CAAA;IACjC;;;;;;;;;;;;;;;;;;;;;OAqBG;IACH,eAAe,EAAE,CACf,UAAU,EAAE,yBAAyB,KAClC,OAAO,CAAC,yBAAyB,CAAC,CAAA;IACvC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA2CG;IACH,cAAc,EAAE,CACd,KAAK,CAAC,SAAS,SAAS,SAAS,OAAO,EAAE,EAC1C,aAAa,SAAS,KAAK,GAAG,SAAS,GAAG,SAAS,EAEnD,UAAU,EAAE,wBAAwB,CAClC,SAAS,EACT,KAAK,EACL,OAAO,EACP,aAAa,CACd,KACE,OAAO,CAAC,wBAAwB,CAAC,CAAA;CACvC,CAAA;AAED;;;;;;;;;;;;;;;;GAgBG;AACH,wBAAgB,cAAc,KAE1B,SAAS,SAAS,SAAS,EAC3B,KAAK,SAAS,KAAK,GAAG,SAAS,sBAC/B,OAAO,SAAS,OAAO,GAAG,SAAS,gCAE3B,MAAM,CAAC,SAAS,EAAE,KAAK,EAAE,OAAO,CAAC,KACxC,cAAc,CAAC,KAAK,EAAE,OAAO,CAAC,CAUlC"}