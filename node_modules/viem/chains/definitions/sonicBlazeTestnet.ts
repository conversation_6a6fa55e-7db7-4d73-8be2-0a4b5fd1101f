import { define<PERSON><PERSON><PERSON> } from '../../utils/chain/defineChain.js'

export const sonicBlazeTestnet = /*#__PURE__*/ define<PERSON>hain({
  id: 57_054,
  name: 'Sonic Blaze Testnet',
  nativeCurrency: {
    decimals: 18,
    name: '<PERSON>',
    symbol: 'S',
  },
  rpcUrls: {
    default: { http: ['https://rpc.blaze.soniclabs.com'] },
  },
  blockExplorers: {
    default: {
      name: 'Sonic Blaze Testnet Explorer',
      url: 'https://testnet.sonicscan.org',
    },
  },
  testnet: true,
})
